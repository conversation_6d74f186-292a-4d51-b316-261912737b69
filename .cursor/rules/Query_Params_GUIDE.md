# Composables Guide

This guide provides documentation for some of the core composables used in this application.

## `useQueryParams`

**File:** `composables/useQueryParams.ts`

This composable provides a reactive way to interact with the URL query parameters. It's a generic utility for reading from and writing to the URL query string.

### Returned Values

-   `queryParams`: A `computed` property that holds an object representing the current query parameters. It is reactive and will update whenever the URL query changes.
-   `setQueryParam(query: QueryParams)`: A function to update the URL query parameters. It merges the provided `query` object with any existing query parameters.

### Type Definitions

```typescript
type QueryParams = Record<string, string | string[] | undefined>
```

### Example Usage

Here's an example of how you might use `useQueryParams` in a component to set a filter and page number.

```vue
<script setup lang="ts">
import { useQueryParams } from '~/composables/useQueryParams'

const { queryParams, setQueryParam } = useQueryParams()

function applyFilter(newFilter: string) {
  setQueryParam({ filter: newFilter, page: '1' })
}
</script>

<template>
  <div>
    <p>Current filter: {{ queryParams.filter }}</p>
    <button @click="applyFilter('active')">
      Show Active
    </button>
  </div>
</template>
```

## `useBaseQuery`

**File:** `composables/useBaseQuery.ts`

This composable builds upon `useQueryParams` to provide common functionalities for lists of data that require searching and pagination. It abstracts the logic for manipulating `q` (search query) and `page` parameters in the URL.

### Returned Values

-   `search(query: string)`: A function that sets the `q` query parameter to the provided search term and resets the `page` parameter to `1`.
-   `nextPage()`: A function that increments the `page` query parameter.
-   `prevPage()`: A function that decrements the `page` query parameter.

### Example Usage

This composable is useful in pages that display paginated and searchable data, like a list of users. It is designed to work with data-fetching composables that react to changes in `queryParams`.

```vue
<script setup lang="ts">
import { useBaseQuery } from '~/composables/useBaseQuery'
import { useQueryParams } from '~/composables/useQueryParams'
import { useUsers } from '~/composables/useUsers'

// `useUsers` is a data-fetching composable that internally
// uses `useQueryParams` to get its parameters.
const { users, pending, refresh } = useUsers()
const { search, nextPage, prevPage } = useBaseQuery()
const { queryParams } = useQueryParams()

// The data-fetching composable should react to query parameter changes.
// `useUsers` already handles this.

function onSearchInput(event: Event) {
  const target = event.target as HTMLInputElement
  search(target.value)
}
</script>

<template>
  <div>
    <input
      :value="queryParams.q"
      placeholder="Search users..."
      @input="onSearchInput"
    >

    <div v-if="pending">
      Loading...
    </div>
    <div v-else>
      <ul>
        <li v-for="user in users?.data" :key="user.id">
          {{ user.name }}
        </li>
      </ul>

      <button
        :disabled="!users?.meta || users.meta.current_page <= 1"
        @click="prevPage"
      >
        Previous Page
      </button>
      <button
        :disabled="!users?.meta || users.meta.current_page === users.meta.last_page"
        @click="nextPage"
      >
        Next Page
      </button>
    </div>
  </div>
</template>
```

This setup creates a clear separation of concerns:
-   **`useQueryParams`**: Manages the state of URL query parameters.
-   **`useBaseQuery`**: Provides actions to manipulate common query parameters for lists.
-   **Page Component**: Handles user interaction and displays data.
-   **Data-fetching composable (`useUsers`)**: Fetches data based on the current `queryParams`.
