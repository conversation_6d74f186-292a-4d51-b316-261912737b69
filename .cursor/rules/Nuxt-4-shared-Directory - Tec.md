# Nuxt 4 shared/ Directory Import Restrictions - Technical Notes

## Core Restriction

**"Code in the shared/ directory cannot import any Vue or Nitro code"** - This is a fundamental constraint designed to ensure **context-agnostic** behavior of the `shared/` directory.

## Rationale Behind This Restriction

### 1. Environment-Agnostic Design

The `shared/` directory is designed to contain code that can safely run in **both environments**: Node.js (server) and browser (client). Importing Vue or Nitro code breaks this independence because:

- **Vue code** can only run in browser environment or environments with DOM
- **Nitro code** can only run in Node.js server environment


### 2. Preventing Runtime Errors During Environment Switching

When code in `shared/` imports Vue or Nitro code, it can lead to runtime errors such as:

```javascript
// Error when shared code imports Vue in server context
ReferenceError: window is not defined

// Error when shared code imports Nitro in browser context
ReferenceError: process is not defined
```


### 3. Preventing Context Bleeding

Keeping `shared/` code completely context-agnostic helps:

- **Clear separation** between client-side and server-side logic
- **Avoid dependency conflicts** when code is used in both environments
- **Easier testing** since code doesn't depend on specific environments


## What Is NOT Allowed - Specific Examples

### ❌ Vue Code Imports

```typescript
// shared/utils/helper.ts - NOT ALLOWED
import { ref, computed } from 'vue'
import MyComponent from '~/components/MyComponent.vue'

export const useHelper = () => {
  const count = ref(0) // Will error in server environment
  return count
}
```


### ❌ Nitro Code Imports

```typescript
// shared/utils/api.ts - NOT ALLOWED
import { defineEventHandler } from 'h3'

export const createHandler = () => {
  return defineEventHandler(() => {}) // Will error in browser environment
}
```


## What IS Allowed in shared/

### ✅ Pure JavaScript/TypeScript Logic

```typescript
// shared/utils/capitalize.ts - ALLOWED
export const capitalize = (input: string) => {
  return input[0] ? input[0].toUpperCase() + input.slice(1) : ''
}
```


### ✅ Types and Interfaces

```typescript
// shared/types/user.ts - ALLOWED
export interface User {
  id: number
  name: string
  email: string
}
```


### ✅ Environment-Independent Business Logic

```typescript
// shared/utils/validation.ts - ALLOWED
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}
```


## Solutions for Complex Logic Sharing

### 1. Create Abstraction Layer

```typescript
// shared/utils/storage.ts - Common interface
export interface StorageAdapter {
  get(key: string): Promise<any>
  set(key: string, value: any): Promise<void>
}

// app/utils/browser-storage.ts - Browser implementation
export class BrowserStorageAdapter implements StorageAdapter {
  async get(key: string) {
    return localStorage.getItem(key)
  }

  async set(key: string, value: any) {
    localStorage.setItem(key, JSON.stringify(value))
  }
}

// server/utils/node-storage.ts - Server implementation
export class NodeStorageAdapter implements StorageAdapter {
  async get(key: string) {
    // Logic for database or file system retrieval
  }

  async set(key: string, value: any) {
    // Logic for database or file system storage
  }
}
```


### 2. Dependency Injection Pattern

```typescript
// shared/utils/service.ts
import type { StorageAdapter } from './storage'

export class DataService {
  constructor(private storage: StorageAdapter) {}

  async getUserData(id: string) {
    return this.storage.get(`user:${id}`)
  }
}
```


## Benefits of This Restriction

| Benefit | Description |
| :-- | :-- |
| **Reliability** | Ensures code works consistently across all environments |
| **Maintainability** | Easier to maintain and debug due to no hidden dependencies |
| **Testability** | Code can be tested independently without environment mocking |
| **Performance** | Avoids bundle size bloat from unnecessary imports |
| **Type Safety** | TypeScript can check more accurately with clear context |

## Key Takeaways

- The restriction may seem limiting but actually promotes **cleaner architecture**
- Prevents many **runtime errors that are difficult to debug**
- Forces developers to think carefully about **separation of concerns** between client and server code
- Encourages the use of **design patterns** like abstraction layers and dependency injection
- Results in more **maintainable and testable** codebases


## Best Practices

1. **Keep shared code pure**: Only include environment-independent logic
2. **Use interfaces for abstraction**: Define contracts in shared/, implement in environment-specific directories
3. **Leverage TypeScript**: Use strong typing to catch potential environment mismatches
4. **Think in terms of data and algorithms**: Focus on business logic rather than framework-specific code
5. **Test shared code independently**: Write tests that don't require specific runtime environments

This restriction is a design decision that promotes better software architecture and prevents common pitfalls in full-stack applications.
