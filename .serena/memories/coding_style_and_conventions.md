# Coding Style and Conventions

## TypeScript Configuration
- Strict mode enabled
- Full type safety required
- Vue 3 Composition API patterns

## ESLint Configuration
- Uses @antfu/eslint-config
- Formatters enabled
- Automatic code formatting

## Vue/Nuxt Patterns
- Pages own their data fetching logic
- Use `useAsyncData` with repository methods for SSR-compatible data fetching
- Repository pattern for all API calls in `app/repositories/`
- URL-driven state with `useQueryParams`
- Component auto-import from `app/components/ui/`

## File Organization
- Main source in `app/` directory (custom srcDir)
- Shared utilities in `shared/`
- Type definitions in `shared/types/api.d.ts` and `app/types/`

## API Patterns
- `useAPI()` for SSR-friendly data fetching
- `useLazyAPI()` for non-critical data
- `useAPICall()` for user-triggered actions
- Consistent `ApiResponse<T>` structure