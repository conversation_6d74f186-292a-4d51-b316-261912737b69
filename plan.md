# Test Prompt API Documentation

## Overview

The Test Prompt API endpoint allows testing of AI prompt generation and response without affecting actual scoring. It provides detailed information about the prompt building process, AI model usage, and response generation.

## Endpoint

**URL**: `POST /api/admin/ai-prompts/test-prompt`
**Authentication**: Required (user.auth middleware)

## Request Format

The request format is identical to the `score-assessments` endpoint:

```json
{
  "unit_id": 1,
  "answers": [
    {
      "assessment_id": 1,
      "answer": "Hello world"
    },
    {
      "assessment_id": 2,
      "answer": "I am fine, thank you"
    }
  ]
}
```

### Request Validation

- `unit_id`: Required integer, must exist in units table
- `answers`: Required array with minimum 1 item
- `answers.*.assessment_id`: Required integer, must exist in assessments table
- `answers.*.answer`: Required string

## Response Format

### Success Response (200)

```json
{
  "success": true,
  "message": "Test prompt generated successfully",
  "data": {
    "final_prompt": "You are an English language learning assessment expert...\n\nSentence completion assessments to score:\nAssessment ID 1: Question \"Complete the sentence\" - Student completed the sentence with \"Hello world\" - Context: Basic greeting context\nAssessment ID 2: Question \"How are you?\" - Student completed the sentence with \"I am fine, thank you\" - Context: Polite response context\n\nPlease respond with a JSON array...",
    "model_info": {
      "model_name": "gemini-2.0-flash",
      "tokens_consumed": 1250,
      "processing_time_ms": 1500.25,
      "input_tokens": 800,
      "output_tokens": 450
    },
    "ai_output": [
      {
        "id": 1,
        "point": "8/10",
        "comment": "Good use of basic greeting. Grammar is correct and appropriate for the context."
      },
      {
        "id": 2,
        "point": "9/10",
        "comment": "Excellent polite response. Perfect grammar and very appropriate for the social context."
      }
    ]
  }
}
```

### Error Responses

**Assessment Not Found (404)**:

```json
{
  "success": false,
  "message": "Assessment not found",
  "error": "Assessment with ID 123 does not exist"
}
```

**Invalid Request Data (400)**:

```json
{
  "success": false,
  "message": "Invalid request data",
  "error": "Validation error details"
}
```

**AI Service Error (500)**:

```json
{
  "success": false,
  "message": "Failed to generate test prompt",
  "error": "An unexpected error occurred while processing your request"
}
```

## Response Fields Explained

### final_prompt

The complete prompt string that was sent to the AI service, including:

- Base prompt template from database
- Formatted assessment data with question, answer, and context
- All placeholder replacements applied

### model_info

Detailed information about the AI model usage:

- `model_name`: The Gemini model used (e.g., "gemini-2.0-flash")
- `tokens_consumed`: Total tokens used (input + output)
- `processing_time_ms`: Time taken for AI processing in milliseconds
- `input_tokens`: Tokens used for the input prompt
- `output_tokens`: Tokens used for the AI response

### ai_output

The parsed and validated JSON response from the AI service, containing:

- `id`: Assessment ID
- `point`: Score given by AI (e.g., "8/10")
- `comment`: Detailed feedback from AI

## New Features Implemented

### 1. Database-Driven AI Parameters

- Temperature and max_tokens values are now stored in the `ai_prompts` table
- Default values: temperature=0.3, max_tokens=2000
- Values are automatically read from database instead of hardcoded

### 2. Question Parameter Support

- Added support for `{question}` placeholder in `item_replace_pattern`
- Questions are automatically extracted from assessment itemable objects
- Backward compatible - missing questions default to empty string

### 3. Enhanced Metadata Tracking

- New `generateStructuredContentWithMetadata()` method in GeminiService
- Tracks processing time, token consumption, and model information
- Provides detailed insights into AI service usage

## Database Changes

### ai_prompts Table

New columns added:

- `temperature` (decimal, 3,2): AI temperature parameter (default: 0.3)
- `max_tokens` (integer): Maximum tokens for AI response (default: 2000)

### Updated Seeder

The AiPromptSeeder now includes:

- Default temperature and max_tokens values
- Updated item_replace_pattern with {question} placeholder:
  ```
  'Assessment ID {id}: Question "{question}" - Student completed the sentence with "{answer}" - Context: {context}'
  ```

## Usage Examples

### Basic Test

```bash
curl -X POST "https://your-api-domain.com/api/ai/test-prompt" \
  -H "Authorization: Bearer YOUR_USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "unit_id": 1,
    "answers": [
      {
        "assessment_id": 1,
        "answer": "The weather is nice today"
      }
    ]
  }'
```

### Multiple Assessments

```bash
curl -X POST "https://your-api-domain.com/api/ai/test-prompt" \
  -H "Authorization: Bearer YOUR_USER_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "unit_id": 2,
    "answers": [
      {
        "assessment_id": 5,
        "answer": "I went to the store"
      },
      {
        "assessment_id": 6,
        "answer": "She is reading a book"
      }
    ]
  }'
```

## Differences from score-assessments

| Feature         | score-assessments  | test-prompt                 |
| --------------- | ------------------ | --------------------------- |
| Purpose         | Actual scoring     | Testing/debugging           |
| Response        | Only AI results    | Prompt + metadata + results |
| Metadata        | None               | Full model info and timing  |
| Database Impact | None               | None                        |
| Use Case        | Production scoring | Development/testing         |

## Technical Implementation

The test-prompt endpoint follows the same validation and processing flow as score-assessments but uses the new `generateStructuredContentWithMetadata()` method to provide additional insights into the AI processing pipeline.

All existing functionality remains unchanged, ensuring backward compatibility while adding powerful new testing capabilities.
