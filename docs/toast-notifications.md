# Toast Notifications Documentation

## Overview

This project uses **vue-sonner** with **shadcn/ui Vue** to provide beautiful, accessible toast notifications throughout the English learning game. The toast system is designed to give users immediate feedback on their actions, progress, and any issues that occur.

## Table of Contents

- [Quick Start](#quick-start)
- [Basic Usage](#basic-usage)
- [Game-Specific Toasts](#game-specific-toasts)
- [Assessment Management Toasts](#assessment-management-toasts)
- [Advanced Features](#advanced-features)
- [Best Practices](#best-practices)
- [API Reference](#api-reference)

## Quick Start

### 1. Import the composable in your component:

```vue
<script setup lang="ts">
// For basic toasts
const { success, error, warning, info, toast } = useToast()

// For game-specific toasts
const gameToasts = useGameToasts()

// For assessment operations with toasts
const assessmentToasts = useAssessmentWithToasts()
</script>
```

### 2. Use in your methods:

```typescript
// Basic success message
success('Level completed!', {
  description: 'You earned 100 points!',
  duration: 3000
})

// Game-specific toast
gameToasts.levelComplete(5, 150, 25) // level, points, timeBonus
```

## Basic Usage

### Available Toast Types

```typescript
const { success, error, warning, info, loading, promise, toast, dismiss } = useToast()

// Success toast (green)
success('Operation successful!', {
  description: 'Your data has been saved',
  duration: 3000
})

// Error toast (red)
error('Something went wrong', {
  description: 'Please try again later',
  duration: 5000
})

// Warning toast (yellow)
warning('Time running out!', {
  description: 'You have 2 minutes left',
  duration: 4000
})

// Info toast (blue)
info('New feature available', {
  description: 'Check out the updated dashboard',
  duration: 3000
})

// Loading toast
const loadingToast = loading('Processing...', {
  description: 'Please wait while we save your progress'
})

// Dismiss a specific toast
dismiss(loadingToast)
```

### Toast with Action Button

```typescript
toast('File uploaded successfully', {
  description: 'Your assessment has been saved',
  action: {
    label: 'View',
    onClick: () => {
      // Navigate to the uploaded file
      navigateTo('/assessments/123')
    }
  }
})
```

### Promise Toast

Perfect for API calls - automatically shows loading, then success or error:

```typescript
const savePromise = assessmentRepository.createAssessment(data)

promise(savePromise, {
  loading: 'Creating assessment...',
  success: (result) => `Assessment "${result.title}" created successfully!`,
  error: (error) => `Failed to create assessment: ${error.message}`
})
```

## Game-Specific Toasts

The `useGameToasts()` composable provides pre-configured toasts for common game events:

### Level and Progress

```typescript
const gameToasts = useGameToasts()

// Level completion
gameToasts.levelComplete(5, 150, 25) // level, points, timeBonus
gameToasts.levelComplete(3, 100) // without time bonus

// Course completion
gameToasts.courseComplete('Grammar Basics', 2500)

// Unit completion
gameToasts.unitComplete('Present Tense', 85) // unitName, accuracy%
```

### Achievements

```typescript
// Simple achievement
gameToasts.achievementUnlocked('Speed Demon')

// Achievement with description
gameToasts.achievementUnlocked('Vocabulary Master', 'Learn 100 new words')

// Streak achievement
gameToasts.streakAchievement(7) // 7-day streak

// Perfect score
gameToasts.perfectScore('Grammar Quiz #1')
```

### Assessment Feedback

```typescript
// Assessment submission
gameToasts.assessmentSubmitted(28, 30) // score, total

// Auto-save notification
gameToasts.assessmentSaved(true) // autoSave = true

// Manual save
gameToasts.assessmentSaved(false)

// Time warnings
gameToasts.timeWarning(5) // 5 minutes left
gameToasts.timeAlmostUp() // < 1 minute
```

### Learning Progress

```typescript
// Vocabulary progress
gameToasts.vocabularyLearned(8) // 8 new words

// Skill improvement
gameToasts.skillImproved('Reading Speed', 'Increased by 15%')

// Daily goal
gameToasts.dailyGoalReached()
```

### Interactive Toasts

```typescript
// Review mistakes with action
gameToasts.reviewMistakes(3, () => {
  // Handle review action
  navigateTo('/review')
})

// Retry assessment
gameToasts.retryAssessment(() => {
  // Handle retry
  window.location.reload()
})

// Continue to next level
gameToasts.continueToNext('Advanced Grammar', () => {
  navigateTo('/levels/advanced-grammar')
})
```

## Assessment Management Toasts

For admin operations, use `useAssessmentWithToasts()`:

```typescript
const assessmentToasts = useAssessmentWithToasts()

// Create assessment with automatic toast feedback
const newAssessment = await assessmentToasts.createAssessment({
  title: 'Grammar Test',
  description: 'Basic grammar assessment',
  // ... other fields
})

// List assessments with loading feedback
const assessments = await assessmentToasts.listAssessments({
  page: 1,
  per_page: 10
})

// Update with promise toast
const updated = await assessmentToasts.updateAssessment(123, updateData)

// Delete with confirmation-style feedback
const success = await assessmentToasts.deleteAssessment(123, 'Grammar Test')
```

## Advanced Features

### Custom Duration

```typescript
// Short notification (2 seconds)
success('Quick save!', { duration: 2000 })

// Long notification (8 seconds)
warning('Important notice', { 
  description: 'Please read carefully',
  duration: 8000 
})

// Persistent (won't auto-dismiss)
error('Critical error', { duration: Infinity })
```

### Dismissing Toasts

```typescript
// Dismiss all toasts
const { dismiss } = useToast()
dismiss()

// Dismiss specific toast
const toastId = loading('Processing...')
setTimeout(() => dismiss(toastId), 3000)
```

### Custom Game Toast

```typescript
gameToasts.customGameToast(
  'success', // type
  'Custom Achievement!', // title
  'You did something amazing!', // description
  5000 // duration
)
```

## Best Practices

### 1. Choose Appropriate Duration

- **Success messages**: 3-4 seconds
- **Error messages**: 5-6 seconds  
- **Warnings**: 4-6 seconds
- **Info messages**: 3-4 seconds
- **Loading states**: Until operation completes

### 2. Provide Helpful Descriptions

```typescript
// ❌ Not helpful
success('Done!')

// ✅ Helpful
success('Assessment saved!', {
  description: 'Your progress has been automatically saved'
})
```

### 3. Use Appropriate Toast Types

```typescript
// ❌ Wrong type
success('Connection failed') 

// ✅ Correct type
error('Connection failed', {
  description: 'Please check your internet connection'
})
```

### 4. Handle Errors Gracefully

```typescript
try {
  await someApiCall()
  success('Operation completed!')
} catch (error) {
  error('Operation failed', {
    description: error.message || 'Please try again later'
  })
}
```

### 5. Use Interactive Toasts for Important Actions

```typescript
// For actions that users might want to undo or follow up on
toast('Assessment submitted', {
  description: 'You can review your answers',
  action: {
    label: 'Review',
    onClick: () => navigateTo('/review')
  }
})
```

### 6. Batch Related Notifications

```typescript
// ❌ Too many toasts at once
success('Level complete!')
success('Achievement unlocked!')
success('New high score!')

// ✅ Stagger them
success('Level complete!')
setTimeout(() => {
  gameToasts.achievementUnlocked('High Scorer')
}, 1500)
```

## Common Patterns

### API Call with Loading State

```typescript
const handleSubmit = async () => {
  const submitPromise = assessmentRepository.submitAnswers(answers)
  
  const result = await promise(submitPromise, {
    loading: 'Submitting your answers...',
    success: (data) => `Assessment submitted! Score: ${data.score}/${data.total}`,
    error: (err) => `Submission failed: ${err.message}`
  })
  
  if (result) {
    // Handle successful submission
    navigateTo('/results')
  }
}
```

### Form Validation Errors

```typescript
const validateAndSave = () => {
  if (!title.value) {
    error('Title is required', {
      description: 'Please enter a title for your assessment'
    })
    return
  }
  
  if (questions.value.length === 0) {
    warning('No questions added', {
      description: 'Add at least one question before saving'
    })
    return
  }
  
  // Proceed with save
  saveAssessment()
}
```

### Auto-save with Minimal Disruption

```typescript
const autoSave = async () => {
  try {
    await saveProgress()
    // Use info toast for auto-save (less intrusive)
    info('Progress saved', { duration: 2000 })
  } catch (error) {
    // Use error for failed auto-save (more important)
    error('Auto-save failed', {
      description: 'Your progress may not be saved'
    })
  }
}
```

## Troubleshooting

### Toast Not Appearing

1. Ensure `<Toaster />` is added to your `app.vue`
2. Check that `vue-sonner/style.css` is imported
3. Verify the composable is imported correctly

### Styling Issues

1. Check that your CSS variables are properly defined
2. Ensure Tailwind CSS is configured correctly
3. Verify the toast container isn't being hidden by z-index issues

### Performance Issues

1. Avoid creating too many toasts simultaneously
2. Use appropriate durations to prevent toast buildup
3. Consider dismissing old toasts before showing new ones

## Migration from Other Toast Libraries

If migrating from other toast libraries:

1. Replace toast library imports with `useToast()`
2. Update toast method calls to match the new API
3. Replace custom styling with shadcn/ui theme variables
4. Test all toast notifications to ensure proper functionality

---

For more examples, visit `/toast-demo` in your application to see interactive demonstrations of all toast types and patterns.
