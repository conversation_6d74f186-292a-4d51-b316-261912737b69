# Documentation

This folder contains comprehensive documentation for the English Learning Game project.

## Toast Notifications

### 📋 [Toast Notifications Guide](./toast-notifications.md)
Complete guide to using toast notifications in your English learning game. Covers basic usage, game-specific toasts, assessment management, and best practices.

**What you'll learn:**
- How to show success, error, warning, and info toasts
- Game-specific notifications (level completion, achievements, etc.)
- Assessment management with automatic toast feedback
- Interactive toasts with action buttons
- Best practices and common patterns

### 🔧 [Toast API Reference](./toast-api-reference.md)
Detailed API documentation for all toast-related composables and methods.

**Includes:**
- `useToast()` - Basic toast functionality
- `useGameToasts()` - Game-specific notifications
- `useAssessmentWithToasts()` - Assessment operations with feedback
- TypeScript interfaces and type definitions
- Configuration options and browser support

### ⚡ [Toast Setup Guide](./toast-setup-guide.md)
Technical setup documentation showing how the toast system was implemented.

**Covers:**
- Installation steps (already completed)
- File structure and organization
- Configuration options
- Integration examples
- Troubleshooting guide

## Quick Start

### For Developers New to the Project

1. **Read the main guide**: Start with [Toast Notifications Guide](./toast-notifications.md)
2. **Try the demo**: Visit `/toast-demo` in your browser
3. **Check the API**: Reference [Toast API Reference](./toast-api-reference.md) when coding

### For Existing Developers

1. **Import the composables**:
   ```typescript
   const { success, error } = useToast()
   const gameToasts = useGameToasts()
   ```

2. **Use in your components**:
   ```typescript
   // Basic usage
   success('Level completed!', { description: 'You earned 100 points!' })
   
   // Game-specific
   gameToasts.levelComplete(5, 150, 25)
   ```

3. **For assessment operations**:
   ```typescript
   const assessmentToasts = useAssessmentWithToasts()
   const result = await assessmentToasts.createAssessment(data)
   ```

## File Organization

```
docs/
├── README.md                    # This file - documentation overview
├── toast-notifications.md      # Main user guide with examples
├── toast-api-reference.md      # Complete API documentation  
└── toast-setup-guide.md       # Technical setup and configuration
```

## Live Examples

Visit these pages in your application to see toasts in action:

- **`/toast-demo`** - Interactive demo of all toast types
- **Basic toasts** - Success, error, warning, info, loading
- **Game toasts** - Level completion, achievements, progress
- **Assessment toasts** - Create, update, delete operations
- **Interactive toasts** - Toasts with action buttons

## Common Use Cases

### 🎮 Game Progress
```typescript
// Level completion
gameToasts.levelComplete(level, points, timeBonus)

// Achievement unlocked  
gameToasts.achievementUnlocked('Speed Demon', 'Complete 5 levels under 10 minutes')

// Perfect score
gameToasts.perfectScore('Grammar Quiz #1')
```

### 📝 Assessment Management
```typescript
// Create with automatic feedback
const assessment = await assessmentToasts.createAssessment(data)

// Delete with confirmation
const success = await assessmentToasts.deleteAssessment(id, title)
```

### ⚠️ Error Handling
```typescript
try {
  await apiCall()
  success('Operation completed!')
} catch (error) {
  error('Operation failed', { description: error.message })
}
```

### 🔄 Loading States
```typescript
// Promise-based loading
promise(apiCall, {
  loading: 'Saving...',
  success: 'Saved successfully!',
  error: 'Save failed'
})

// Manual loading control
const loadingId = loading('Processing...')
// ... do work ...
dismiss(loadingId)
success('Done!')
```

## Integration Patterns

### In Vue Components
```vue
<script setup lang="ts">
const { success, error } = useToast()
const gameToasts = useGameToasts()

const handleAction = async () => {
  try {
    await performAction()
    success('Action completed!')
  } catch (err) {
    error('Action failed', { description: err.message })
  }
}
</script>
```

### In Composables
```typescript
export const useGameLogic = () => {
  const gameToasts = useGameToasts()
  
  const completeLevel = (level: number, score: number) => {
    // Game logic...
    gameToasts.levelComplete(level, score)
  }
  
  return { completeLevel }
}
```

### In Repositories
```typescript
export const enhancedRepository = {
  async create(data) {
    const { promise } = useToast()
    return promise(repository.create(data), {
      loading: 'Creating...',
      success: 'Created successfully!',
      error: 'Creation failed'
    })
  }
}
```

## Best Practices Summary

1. **Choose appropriate toast types** - Success for completions, error for failures
2. **Provide helpful descriptions** - Give users context about what happened
3. **Use appropriate durations** - Longer for errors, shorter for success
4. **Handle loading states** - Show progress for long operations
5. **Add action buttons** - For important follow-up actions
6. **Avoid toast spam** - Don't overwhelm users with notifications
7. **Test thoroughly** - Verify toasts work in all scenarios

## Contributing

When adding new toast functionality:

1. **Update documentation** - Add examples to the main guide
2. **Add to API reference** - Document new methods and interfaces
3. **Create examples** - Add to the demo page
4. **Test thoroughly** - Verify in different scenarios
5. **Follow patterns** - Use existing composable patterns

## Support

- **Demo page**: `/toast-demo` - Interactive examples
- **Browser console**: Check for errors and warnings
- **TypeScript**: Use IDE intellisense for method signatures
- **Documentation**: These docs cover all functionality

---

**Need help?** Check the troubleshooting section in the [Setup Guide](./toast-setup-guide.md) or review the examples in the [main documentation](./toast-notifications.md).
