import { toast } from 'vue-sonner'

export interface ToastOptions {
  title?: string
  description?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

export const useToast = () => {
  const showToast = (message: string, options?: ToastOptions) => {
    return toast(message, {
      description: options?.description,
      duration: options?.duration,
      action: options?.action,
    })
  }

  const showSuccess = (message: string, options?: Omit<ToastOptions, 'title'>) => {
    return toast.success(message, {
      description: options?.description,
      duration: options?.duration,
      action: options?.action,
    })
  }

  const showError = (message: string, options?: Omit<ToastOptions, 'title'>) => {
    return toast.error(message, {
      description: options?.description,
      duration: options?.duration,
      action: options?.action,
    })
  }

  const showWarning = (message: string, options?: Omit<ToastOptions, 'title'>) => {
    return toast.warning(message, {
      description: options?.description,
      duration: options?.duration,
      action: options?.action,
    })
  }

  const showInfo = (message: string, options?: Omit<ToastOptions, 'title'>) => {
    return toast.info(message, {
      description: options?.description,
      duration: options?.duration,
      action: options?.action,
    })
  }

  const showLoading = (message: string, options?: Omit<ToastOptions, 'title'>) => {
    return toast.loading(message, {
      description: options?.description,
      duration: options?.duration,
    })
  }

  const showPromise = <T>(
    promise: Promise<T>,
    messages: {
      loading: string
      success: string | ((data: T) => string)
      error: string | ((error: any) => string)
    },
    options?: Omit<ToastOptions, 'title'>
  ) => {
    return toast.promise(promise, {
      loading: messages.loading,
      success: messages.success,
      error: messages.error,
      description: options?.description,
      duration: options?.duration,
      action: options?.action,
    })
  }

  const dismiss = (toastId?: string | number) => {
    return toast.dismiss(toastId)
  }

  return {
    toast: showToast,
    success: showSuccess,
    error: showError,
    warning: showWarning,
    info: showInfo,
    loading: showLoading,
    promise: showPromise,
    dismiss,
  }
}
