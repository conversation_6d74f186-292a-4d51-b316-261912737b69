/**
 * Game-specific toast notifications
 * Provides pre-configured toast messages for common game events
 */
export const useGameToasts = () => {
  const { success, error, warning, info, toast } = useToast()

  return {
    // Level and Progress Toasts
    levelComplete: (level: number, points: number, timeBonus?: number) => {
      const description = timeBonus 
        ? `🎯 ${points} points + ${timeBonus} time bonus!`
        : `🎯 You earned ${points} points!`
      
      success(`Level ${level} Complete!`, {
        description,
        duration: 4000,
      })
    },

    courseComplete: (courseName: string, totalPoints: number) => {
      success('🎉 Course Completed!', {
        description: `Congratulations! You finished "${courseName}" with ${totalPoints} total points!`,
        duration: 6000,
      })
    },

    unitComplete: (unitName: string, accuracy: number) => {
      success('Unit Completed!', {
        description: `"${unitName}" finished with ${accuracy}% accuracy`,
        duration: 4000,
      })
    },

    // Achievement Toasts
    achievementUnlocked: (achievementName: string, description?: string) => {
      info('🏆 Achievement Unlocked!', {
        description: description || achievementName,
        duration: 5000,
      })
    },

    streakAchievement: (days: number) => {
      info(`🔥 ${days}-Day Streak!`, {
        description: `You've been learning consistently for ${days} days!`,
        duration: 4000,
      })
    },

    perfectScore: (assessmentName?: string) => {
      success('🌟 Perfect Score!', {
        description: assessmentName 
          ? `100% on "${assessmentName}"! Outstanding work!`
          : '100% correct! Outstanding work!',
        duration: 5000,
      })
    },

    // Assessment and Quiz Toasts
    assessmentSaved: (autoSave = false) => {
      if (autoSave) {
        info('Progress saved', {
          description: 'Your answers have been automatically saved',
          duration: 2000,
        })
      } else {
        success('Assessment saved', {
          description: 'Your progress has been saved successfully',
          duration: 3000,
        })
      }
    },

    assessmentSubmitted: (score?: number, total?: number) => {
      const description = score !== undefined && total !== undefined
        ? `Your score: ${score}/${total} (${Math.round((score / total) * 100)}%)`
        : 'Your answers have been submitted for review'
      
      success('Assessment submitted!', {
        description,
        duration: 4000,
      })
    },

    timeWarning: (minutesLeft: number) => {
      warning(`⏰ ${minutesLeft} minutes remaining!`, {
        description: 'Don\'t forget to submit your answers',
        duration: 6000,
      })
    },

    timeAlmostUp: () => {
      warning('⚠️ Time almost up!', {
        description: 'You have less than 1 minute remaining',
        duration: 8000,
      })
    },

    // Error and Connection Toasts
    connectionError: () => {
      error('Connection lost', {
        description: 'Please check your internet connection and try again',
        duration: 5000,
      })
    },

    saveError: () => {
      error('Failed to save progress', {
        description: 'Your answers may not be saved. Please try again.',
        duration: 5000,
      })
    },

    loadError: (itemType = 'content') => {
      error(`Failed to load ${itemType}`, {
        description: 'Please refresh the page or try again later',
        duration: 4000,
      })
    },

    // Learning Progress Toasts
    vocabularyLearned: (wordCount: number) => {
      success(`📚 ${wordCount} new words learned!`, {
        description: 'Keep up the great work!',
        duration: 3000,
      })
    },

    skillImproved: (skillName: string, improvement: string) => {
      info(`📈 ${skillName} improved!`, {
        description: improvement,
        duration: 4000,
      })
    },

    dailyGoalReached: () => {
      success('🎯 Daily goal reached!', {
        description: 'You\'ve completed your learning goal for today!',
        duration: 4000,
      })
    },

    // Interactive Toasts with Actions
    reviewMistakes: (mistakeCount: number, onReview: () => void) => {
      toast(`Found ${mistakeCount} areas to improve`, {
        description: 'Would you like to review them now?',
        duration: 8000,
        action: {
          label: 'Review',
          onClick: onReview,
        },
      })
    },

    retryAssessment: (onRetry: () => void) => {
      toast('Assessment completed', {
        description: 'You can retake this assessment to improve your score',
        duration: 6000,
        action: {
          label: 'Retry',
          onClick: onRetry,
        },
      })
    },

    continueToNext: (nextItemName: string, onContinue: () => void) => {
      toast('Great job!', {
        description: `Ready to continue to "${nextItemName}"?`,
        duration: 5000,
        action: {
          label: 'Continue',
          onClick: onContinue,
        },
      })
    },

    // Utility functions
    customGameToast: (
      type: 'success' | 'error' | 'warning' | 'info',
      title: string,
      description?: string,
      duration = 3000
    ) => {
      const toastFn = { success, error, warning, info }[type]
      toastFn(title, { description, duration })
    },
  }
}
