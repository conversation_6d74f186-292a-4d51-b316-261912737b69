import type { LaravelPaginatedResponse } from './api'

/**
 * AI Prompt Management Types
 * Defines interfaces for AI prompt system used in assessment generation
 */

export interface AiPrompt {
  id: number
  type: string
  prompt: string
  item_replace_pattern: string
  default_context: string
  created_at: string
  updated_at: string
}

export interface UpdateAiPromptData {
  prompt: string // AI prompt template with {variable_placeholders}
  item_replace_pattern: string // Pattern like "ID {id}: Answer '{answer}' - Context: {context}"
  default_context: string // Default context when none provided
}

export interface AiPromptListQuery {
  per_page?: number
  q?: string // Search query
  page?: number | string
}

// Response Types
export type AiPromptListResponse = LaravelPaginatedResponse<AiPrompt>
