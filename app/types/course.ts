import type { LaravelPaginatedResponse } from './api'

// Enums for skill types and difficulties
export type SkillType = 'vocabulary' | 'grammar' | 'listening' | 'reading' | 'speaking' | 'writing'
export type Difficulty = 'beginner' | 'elementary' | 'intermediate' | 'upper_intermediate' | 'advanced' | 'proficient'
export type AssessmentType = 'multiple-select' | 'ai-gap-fill-sentence'

// Course Types
export interface Course {
  id: number
  title: string
  description: string
  created_at: string
  updated_at: string
  units?: Unit[]
}

export interface CreateCourseData {
  title: string
  description: string
}

export interface UpdateCourseData {
  title?: string
  description?: string
}

export interface CourseListQuery {
  per_page?: number
}

export interface CourseWithStats extends Course {
  units_count?: number
  assessments_count?: number
}

// Unit Types
export interface Unit {
  id: number
  course_id: number
  title: string
  description: string
  skill_type: SkillType
  difficulty: Difficulty
  unit_order: number
  created_at?: string
  updated_at?: string
  course?: Pick<Course, 'id' | 'title'>
  assessments?: Assessmentable[]
  unit_type: AssessmentType
}

export interface CreateUnitData {
  course_id: number
  title: string
  description: string
  skill_type: SkillType
  difficulty: Difficulty
  unit_order: number
}

export interface UpdateUnitData {
  title?: string
  description?: string
  skill_type?: SkillType
  difficulty?: Difficulty
  unit_order?: number
}

export interface UnitListQuery {
  course_id?: number
  skill_type?: SkillType
  difficulty?: Difficulty
  per_page?: number
}

export interface MoveUnitData {
  target_course_id: number
  target_order: number
}

export interface ReorderUnitData {
  new_order: number
}

export interface DuplicateUnitData {
  target_order: number
}

// Assessment Types
export interface Assessment {
  id: number
  created_at?: string
  updated_at?: string
  assessment?: {
    id: number
    units: Array<Pick<Unit, 'id' | 'title'>>
  }
}

export type Assessmentable = AssessmentMultiSelect | AssessmentAiGapFillSentence

export interface AssessmentMultiSelect extends Assessment {
  question: string
  answer_list: string[]
  correct_answer_indexes: number[]
  explanations: string[]
}

export interface AssessmentAiGapFillSentence extends Assessment {
  context: string
  question: string
  fill_position: number[]
}

export interface UnitAttachment {
  unit_id: number
  assessment_order: number
}

export interface CreateAssessmentData {
  question: string
  answer_list: string[]
  correct_answer_indexes: number[]
  explanations: string[]
  unit_attachments?: Omit<UnitAttachment, 'assessment_order'>[]
}

export interface UpdateAssessmentData {
  question?: string
  answer_list?: string[]
  correct_answer_indexes?: number[]
  explanations?: string[]
}

export interface AttachUnitsData {
  unit_attachments: UnitAttachment[]
}

export interface DetachUnitsData {
  unit_ids: number[]
}

export interface AssessmentListQuery {
  per_page?: number
  unit_id?: number
}

// CSV Import Types
export interface CSVRow {
  unit_title: string
  unit_description?: string
  skill_type: SkillType
  difficulty: Difficulty
  question: string
  option_1: string
  option_2: string
  option_3?: string
  option_4?: string
  correct_answer: string
  explanation?: string
}

export interface ImportSummary {
  units_created: number
  assessments_created: number
}

export interface ImportResponse {
  summary: ImportSummary
  created_units: Unit[]
}

export interface ValidationResponse {
  is_valid: boolean
  total_rows: number
  estimated_units: number
  estimated_assessments: number
  preview: CSVRow[]
  errors?: string[]
}

export interface CSVImportData {
  csv_file: File
  course_id: number
}

// Response Types
export type CourseListResponse = LaravelPaginatedResponse<Course>
export type UnitListResponse = LaravelPaginatedResponse<Unit>
export type AssessmentListResponse = LaravelPaginatedResponse<Assessmentable>
export type CourseStatsResponse = LaravelPaginatedResponse<CourseWithStats>
