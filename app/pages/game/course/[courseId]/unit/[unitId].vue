<script setup lang="ts">
import type { Assessmentable, AssessmentAiGapFillSentence, AssessmentMultiSelect } from '@/types/course'
import { HelpCircle } from 'lucide-vue-next'
import { AssessmentType } from '~~/shared/constants'
import GameOverModal from '@/components/modals/GameOverModal.vue'
import LevelCompleteModal from '@/components/modals/LevelCompleteModal.vue'
import { courseRepository } from '@/repositories/course'
import { unitRepository } from '@/repositories/unit'
import { useProgressStore } from '@/stores/progress'
import AiGapFillGameItem from '~/components/assessments/AssessmentAiGapFillSentence/AiGapFillGameItem.vue'
import MultiselectGameItem from '~/components/assessments/MultiSelect/MultiselectGameItem.vue'

// Route parameters
const route = useRoute()
const courseId = ref(Number(route.params.courseId))
const unitId = ref(Number(route.params.unitId))

// Modal system
const modal = useModal()

// Progress store
const progressStore = useProgressStore()

// Track game start time for time spent calculation
const gameStartTime = ref(Date.now())

// Fetch course and unit data
const { data: course, pending: courseLoading } = await useAsyncData(
  `course-${courseId.value}`,
  () => courseRepository.getPublicCourse(courseId.value),
  {
    server: true,
    default: () => null,
  },
)

const { data: unit, pending: unitLoading } = await useAsyncData(
  `unit-${unitId.value}`,
  () => unitRepository.getPublicUnit(unitId.value),
  {
    server: true,
    default: () => null,
  },
)

// Get assessments as Assessmentable objects
const assessments = computed(() => {
  if (!unit.value?.assessments)
    return []
  return unit.value.assessments as Assessmentable[]
})

// Game state - updated to handle both assessment types
const gameState = ref({
  assessments: assessments.value,
  currentAssessmentIndex: 0,
  currentAssessment: assessments.value[0] || null,
  // MultiSelect specific state
  selectedAnswer: null as number | null,
  answerSelected: false,
  // AI Gap Fill specific state
  userAnswers: [] as string[],
  answerSubmitted: false,
  // Common state
  showFeedback: false,
  isCorrect: false,
  lives: 3,
  maxLives: 3,
  score: 0,
  loading: false,
  error: null,
})

// Computed properties
const currentAssessment = computed(() => gameState.value.currentAssessment)
const currentAssessmentIndex = computed(() => gameState.value.currentAssessmentIndex)
const selectedAnswer = computed(() => gameState.value.selectedAnswer)
const answerSelected = computed(() => gameState.value.answerSelected)
const userAnswers = computed(() => gameState.value.userAnswers)
const answerSubmitted = computed(() => gameState.value.answerSubmitted)
const showFeedback = computed(() => gameState.value.showFeedback)
const isCorrect = computed(() => gameState.value.isCorrect)
const lives = computed(() => gameState.value.lives)
const maxLives = computed(() => gameState.value.maxLives)
const score = computed(() => gameState.value.score)
const totalAssessments = computed(() => gameState.value.assessments.length)
const isLastQuestion = computed(() => currentAssessmentIndex.value >= totalAssessments.value - 1)
const progressPercentage = computed(() => {
  if (totalAssessments.value === 0)
    return 0
  return Math.round(((currentAssessmentIndex.value + 1) / totalAssessments.value) * 100)
})

// Game logic for MultiSelect assessments
function selectAnswer(answerIndex: number) {
  if (gameState.value.answerSelected || unit.value?.unit_type !== AssessmentType.MULTIPLE_SELECT)
    return

  gameState.value.selectedAnswer = answerIndex
  gameState.value.answerSelected = true

  const assessment = gameState.value.currentAssessment as AssessmentMultiSelect
  if (!assessment)
    return

  const isAnswerCorrect = assessment.correct_answer_indexes.includes(answerIndex)
  gameState.value.isCorrect = isAnswerCorrect

  if (isAnswerCorrect) {
    gameState.value.score += 10
  }
  else {
    gameState.value.lives--
    if (gameState.value.lives <= 0) {
      // Game over
      setTimeout(() => {
        showGameOverModal()
      }, 1500)
      return
    }
  }

  gameState.value.showFeedback = true
}

// Game logic for AI Gap Fill assessments
function updateGapAnswer(gapIndex: number, value: string) {
  if (gameState.value.answerSubmitted || unit.value?.unit_type !== AssessmentType.AI_GAP_FILL_SENTENCE)
    return

  // Ensure userAnswers array is large enough
  while (gameState.value.userAnswers.length <= gapIndex) {
    gameState.value.userAnswers.push('')
  }

  gameState.value.userAnswers[gapIndex] = value
}

function submitGapFillAnswer() {
  if (gameState.value.answerSubmitted || unit.value?.unit_type !== AssessmentType.AI_GAP_FILL_SENTENCE)
    return

  gameState.value.answerSubmitted = true

  const assessment = gameState.value.currentAssessment as AssessmentAiGapFillSentence
  if (!assessment)
    return

  // For now, we'll consider it correct if all gaps are filled
  // In a real implementation, you'd validate against expected answers
  const allGapsFilled = assessment.fill_position?.every((_, index) => {
    return (gameState.value.userAnswers[index]?.trim().length || 0) > 0
  }) || false

  gameState.value.isCorrect = allGapsFilled

  if (allGapsFilled) {
    gameState.value.score += 10
  }
  else {
    gameState.value.lives--
    if (gameState.value.lives <= 0) {
      // Game over
      setTimeout(() => {
        showGameOverModal()
      }, 1500)
      return
    }
  }

  gameState.value.showFeedback = true
}

function nextQuestion() {
  if (isLastQuestion.value) {
    // Unit completed
    completeUnit()
  }
  else {
    // Next assessment
    gameState.value.currentAssessmentIndex++
    gameState.value.currentAssessment = gameState.value.assessments[gameState.value.currentAssessmentIndex] || null
    resetAssessmentState()
  }
}

function resetAssessmentState() {
  // Reset MultiSelect state
  gameState.value.selectedAnswer = null
  gameState.value.answerSelected = false
  // Reset AI Gap Fill state
  gameState.value.userAnswers = []
  gameState.value.answerSubmitted = false
  // Reset common state
  gameState.value.showFeedback = false
  gameState.value.isCorrect = false
}

function resetGameState() {
  gameState.value.lives = gameState.value.maxLives
  gameState.value.score = 0
  gameState.value.currentAssessmentIndex = 0
  gameState.value.currentAssessment = gameState.value.assessments[0] || null
  resetAssessmentState()
}

async function showGameOverModal() {
  const action = await modal.custom(GameOverModal, {
    finalScore: gameState.value.score,
    questionsAnswered: gameState.value.currentAssessmentIndex + 1,
    totalQuestions: totalAssessments.value,
  }, {
    title: 'Game Over',
    size: 'md',
    persistent: true,
  })

  if (action === 'retry') {
    resetGameState()
  }
  else {
    goBack()
  }
}

async function completeUnit() {
  // Calculate time spent
  const timeSpent = Math.floor((Date.now() - gameStartTime.value) / 1000)

  // Calculate correct answers more accurately
  const correctAnswers = gameState.value.assessments.reduce((count, assessment, index) => {
    if (index <= gameState.value.currentAssessmentIndex) {
      // Check if the question was answered correctly based on score increase
      const expectedScore = (index + 1) * 10
      return count + (gameState.value.score >= expectedScore ? 1 : 0)
    }
    return count
  }, 0)

  // Save progress to store
  progressStore.completeUnit({
    unitId: unitId.value,
    courseId: courseId.value,
    courseTitle: course.value?.title || 'Unknown Course',
    score: gameState.value.score,
    totalQuestions: totalAssessments.value,
    correctAnswers,
    timeSpent,
  })

  const achievements: string[] = []
  if (correctAnswers === totalAssessments.value) {
    achievements.push('Perfect Score!')
  }
  if (gameState.value.lives === gameState.value.maxLives) {
    achievements.push('Flawless Victory!')
  }

  const action = await modal.custom(LevelCompleteModal, {
    stage: courseId.value,
    level: unitId.value,
    score: gameState.value.score,
    correctAnswers,
    totalQuestions: totalAssessments.value,
    isLastLevel: false, // We don't know if this is the last unit
    achievements,
  }, {
    title: 'Unit Complete!',
    size: 'md',
    persistent: true,
  })

  if (action === 'next') {
    // Try to find the next unit in the course
    await findAndNavigateToNextUnit()
  }
  else if (action === 'replay') {
    // Reset unit
    resetGameState()
  }
  else {
    goBack()
  }
}

async function findAndNavigateToNextUnit() {
  try {
    // Get all units for this course
    const unitsResponse = await unitRepository.getUnitsByCourse(courseId.value)

    // The API call composable extracts the data property, so unitsResponse should be the paginated response
    const units = unitsResponse.data || []

    // Sort units by unit_order to ensure proper sequence
    const sortedUnits = units.sort((a, b) => a.unit_order - b.unit_order)

    // Find current unit
    const currentUnit = sortedUnits.find(u => u.id === unitId.value)

    if (!currentUnit) {
      throw new Error('Current unit not found in course units')
    }

    // Find next unit by unit_order
    const nextUnit = sortedUnits.find(u => u.unit_order > currentUnit.unit_order)

    if (nextUnit) {
      // Navigate to next unit
      await navigateTo(`/game/course/${courseId.value}/unit/${nextUnit.id}`)
    }
    else {
      // No more units, course completed! Navigate to course completion page
      await navigateTo(`/course-complete/${courseId.value}`)
    }
  }
  catch (error) {
    console.error('Error finding next unit:', error)
    await modal.alert({
      title: 'Error',
      description: 'Failed to load the next unit. Returning to course selection.',
      variant: 'destructive',
    })
    goBack()
  }
}

function goBack() {
  navigateTo('/')
}

// Watch for route changes
watch([courseId, unitId], async () => {
  // Refetch data when route changes
  await refreshCookie(`course-${courseId.value}`)
  await refreshCookie(`unit-${unitId.value}`)

  // Reset game state
  resetGameState()
})

// Set page meta
useHead({
  title: computed(() => {
    if (course.value && unit.value) {
      return `${course.value.title} - ${unit.value.title}`
    }
    return 'English Learning Game'
  }),
  meta: [
    { name: 'description', content: 'Learn English through interactive questions and assessments.' },
  ],
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-blue-900">
    <div class="container mx-auto px-4 py-8">
      <!-- Loading State -->
      <div v-if="courseLoading || unitLoading" class="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4" />
        <p class="text-gray-600 dark:text-gray-300">
          Loading unit...
        </p>
      </div>

      <!-- Error State -->
      <div v-else-if="!course || !unit" class="text-center py-12">
        <div class="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
          <HelpCircle class="w-8 h-8 text-red-600 dark:text-red-400" />
        </div>
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">
          Unit Not Found
        </h3>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
          The requested unit could not be found.
        </p>
        <div class="space-x-4">
          <Button @click="goBack">
            Back to Home
          </Button>
        </div>
      </div>

      <!-- No Assessments State -->
      <div v-else-if="assessments.length === 0" class="text-center py-12">
        <div class="w-16 h-16 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center mx-auto mb-4">
          <HelpCircle class="w-8 h-8 text-yellow-600 dark:text-yellow-400" />
        </div>
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">
          No Assessments Available
        </h3>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
          This unit doesn't have any assessments yet.
        </p>
        <div class="space-x-4">
          <Button @click="goBack">
            Back to Home
          </Button>
        </div>
      </div>

      <!-- Game Content -->
      <template v-else-if="currentAssessment">
        <!-- Game Header -->
        <GameNavigation
          :current-stage="courseId"
          :current-level="unitId"
          :current-question-index="currentAssessmentIndex"
          :total-questions="totalAssessments"
          :lives="lives"
          :max-lives="maxLives"
          :score="score"
          :progress-percentage="progressPercentage"
          @go-back="goBack"
        />

        <!-- Course and Unit Info -->
        <Card class="mb-6">
          <CardContent class="p-4">
            <div class="text-center">
              <h2 class="text-lg font-semibold text-gray-800 dark:text-white">
                {{ course.title }}
              </h2>
              <p class="text-sm text-gray-600 dark:text-gray-300">
                {{ unit.title }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {{ unit.description }}
              </p>
            </div>
          </CardContent>
        </Card>

        <!-- Game Content -->
        <main class="max-w-4xl mx-auto">
          <!-- MultiSelect Assessment -->
          <MultiselectGameItem
            v-if="unit.unit_type === AssessmentType.MULTIPLE_SELECT"
            :assessment="currentAssessment as AssessmentMultiSelect"
            :selected-answer="selectedAnswer"
            :answer-selected="answerSelected"
            :show-feedback="showFeedback"
            :is-correct="isCorrect"
            :is-last-question="isLastQuestion"
            class="mb-8"
            @select-answer="selectAnswer"
            @next-question="nextQuestion"
          />

          <!-- AI Gap Fill Assessment -->
          <AiGapFillGameItem
            v-else-if="unit.unit_type === AssessmentType.AI_GAP_FILL_SENTENCE"
            :assessment="currentAssessment as AssessmentAiGapFillSentence"
            :user-answers="userAnswers"
            :answer-submitted="answerSubmitted"
            :show-feedback="showFeedback"
            :is-correct="isCorrect"
            :is-last-question="isLastQuestion"
            class="mb-8"
            @update-answer="updateGapAnswer"
            @submit-answer="submitGapFillAnswer"
            @next-question="nextQuestion"
          />
        </main>
      </template>
    </div>
  </div>
</template>
