<script setup lang="ts">
import type { Assessment } from '@/types/course'
import type { GameState } from '@/types/question'
import { HelpCircle } from 'lucide-vue-next'
import GameOverModal from '@/components/modals/GameOverModal.vue'
import LevelCompleteModal from '@/components/modals/LevelCompleteModal.vue'
import { courseRepository } from '@/repositories/course'
import { unitRepository } from '@/repositories/unit'
import { useProgressStore } from '@/stores/progress'

// Route parameters
const route = useRoute()
const courseId = ref(Number(route.params.courseId))
const unitId = ref(Number(route.params.unitId))

// Modal system
const modal = useModal()

// Progress store
const progressStore = useProgressStore()

// Track game start time for time spent calculation
const gameStartTime = ref(Date.now())

// Fetch course and unit data
const { data: course, pending: courseLoading } = await useAsyncData(
  `course-${courseId.value}`,
  () => courseRepository.getPublicCourse(courseId.value),
  {
    server: true,
    default: () => null,
  },
)

const { data: unit, pending: unitLoading } = await useAsyncData(
  `unit-${unitId.value}`,
  () => unitRepository.getPublicUnit(unitId.value),
  {
    server: true,
    default: () => null,
  },
)

// Convert assessments to questions format
const questions = computed(() => {
  if (!unit.value?.assessments)
    return []

  return unit.value.assessments.map((assessment: Assessment, _index: number) => ({
    id: assessment.id,
    question: assessment.question,
    options: assessment.answer_list.map((answer: string, answerIndex: number) => ({
      text: answer,
      isCorrect: assessment.correct_answer_indexes.includes(answerIndex),
    })),
    explanation: assessment.explanations?.[0] || 'No explanation provided.',
    correctAnswers: assessment.correct_answer_indexes,
  }))
})

// Game state
const gameState = ref<GameState>({
  questions: questions.value,
  currentQuestionIndex: 0,
  currentQuestion: questions.value[0] || null,
  selectedAnswer: null,
  answerSelected: false,
  showFeedback: false,
  isCorrect: false,
  lives: 3,
  maxLives: 3,
  score: 0,
  loading: false,
  error: null,
})

// Computed properties
const currentQuestion = computed(() => gameState.value.currentQuestion)
const currentQuestionIndex = computed(() => gameState.value.currentQuestionIndex)
const selectedAnswer = computed(() => gameState.value.selectedAnswer)
const answerSelected = computed(() => gameState.value.answerSelected)
const showFeedback = computed(() => gameState.value.showFeedback)
const isCorrect = computed(() => gameState.value.isCorrect)
const lives = computed(() => gameState.value.lives)
const maxLives = computed(() => gameState.value.maxLives)
const score = computed(() => gameState.value.score)
const totalQuestions = computed(() => gameState.value.questions.length)
const isLastQuestion = computed(() => currentQuestionIndex.value >= totalQuestions.value - 1)
const progressPercentage = computed(() => {
  if (totalQuestions.value === 0)
    return 0
  return Math.round(((currentQuestionIndex.value + 1) / totalQuestions.value) * 100)
})

// Game logic
function selectAnswer(answerIndex: number) {
  if (gameState.value.answerSelected)
    return

  gameState.value.selectedAnswer = answerIndex
  gameState.value.answerSelected = true

  const question = gameState.value.currentQuestion
  if (!question)
    return

  const isAnswerCorrect = question.options[answerIndex]?.isCorrect || false
  gameState.value.isCorrect = isAnswerCorrect

  if (isAnswerCorrect) {
    gameState.value.score += 10
  }
  else {
    gameState.value.lives--
    if (gameState.value.lives <= 0) {
      // Game over
      setTimeout(() => {
        showGameOverModal()
      }, 1500)
      return
    }
  }

  gameState.value.showFeedback = true
}

function nextQuestion() {
  if (isLastQuestion.value) {
    // Unit completed
    completeUnit()
  }
  else {
    // Next question
    gameState.value.currentQuestionIndex++
    gameState.value.currentQuestion = gameState.value.questions[gameState.value.currentQuestionIndex] || null
    resetQuestionState()
  }
}

function resetQuestionState() {
  gameState.value.selectedAnswer = null
  gameState.value.answerSelected = false
  gameState.value.showFeedback = false
  gameState.value.isCorrect = false
}

function resetGameState() {
  gameState.value.lives = gameState.value.maxLives
  gameState.value.score = 0
  gameState.value.currentQuestionIndex = 0
  gameState.value.currentQuestion = gameState.value.questions[0] || null
  resetQuestionState()
}

async function showGameOverModal() {
  const action = await modal.custom(GameOverModal, {
    finalScore: gameState.value.score,
    questionsAnswered: gameState.value.currentQuestionIndex + 1,
    totalQuestions: totalQuestions.value,
  }, {
    title: 'Game Over',
    size: 'md',
    persistent: true,
  })

  if (action === 'retry') {
    resetGameState()
  }
  else {
    goBack()
  }
}

async function completeUnit() {
  // Calculate time spent
  const timeSpent = Math.floor((Date.now() - gameStartTime.value) / 1000)

  // Calculate correct answers more accurately
  const correctAnswers = gameState.value.questions.reduce((count, question, index) => {
    if (index <= gameState.value.currentQuestionIndex) {
      // Check if the question was answered correctly based on score increase
      const expectedScore = (index + 1) * 10
      return count + (gameState.value.score >= expectedScore ? 1 : 0)
    }
    return count
  }, 0)

  // Save progress to store
  progressStore.completeUnit({
    unitId: unitId.value,
    courseId: courseId.value,
    courseTitle: course.value?.title || 'Unknown Course',
    score: gameState.value.score,
    totalQuestions: totalQuestions.value,
    correctAnswers,
    timeSpent,
  })

  const achievements: string[] = []
  if (correctAnswers === totalQuestions.value) {
    achievements.push('Perfect Score!')
  }
  if (gameState.value.lives === gameState.value.maxLives) {
    achievements.push('Flawless Victory!')
  }

  const action = await modal.custom(LevelCompleteModal, {
    stage: courseId.value,
    level: unitId.value,
    score: gameState.value.score,
    correctAnswers,
    totalQuestions: totalQuestions.value,
    isLastLevel: false, // We don't know if this is the last unit
    achievements,
  }, {
    title: 'Unit Complete!',
    size: 'md',
    persistent: true,
  })

  if (action === 'next') {
    // Try to find the next unit in the course
    await findAndNavigateToNextUnit()
  }
  else if (action === 'replay') {
    // Reset unit
    resetGameState()
  }
  else {
    goBack()
  }
}

async function findAndNavigateToNextUnit() {
  try {
    // Get all units for this course
    const unitsResponse = await unitRepository.getUnitsByCourse(courseId.value)

    // The API call composable extracts the data property, so unitsResponse should be the paginated response
    const units = unitsResponse.data || []

    // Sort units by unit_order to ensure proper sequence
    const sortedUnits = units.sort((a, b) => a.unit_order - b.unit_order)

    // Find current unit
    const currentUnit = sortedUnits.find(u => u.id === unitId.value)

    if (!currentUnit) {
      throw new Error('Current unit not found in course units')
    }

    // Find next unit by unit_order
    const nextUnit = sortedUnits.find(u => u.unit_order > currentUnit.unit_order)

    if (nextUnit) {
      // Navigate to next unit
      await navigateTo(`/game/course/${courseId.value}/unit/${nextUnit.id}`)
    }
    else {
      // No more units, course completed! Navigate to course completion page
      await navigateTo(`/course-complete/${courseId.value}`)
    }
  }
  catch (error) {
    console.error('Error finding next unit:', error)
    await modal.alert({
      title: 'Error',
      description: 'Failed to load the next unit. Returning to course selection.',
      variant: 'destructive',
    })
    goBack()
  }
}

function goBack() {
  navigateTo('/')
}

// Watch for route changes
watch([courseId, unitId], async () => {
  // Refetch data when route changes
  await refreshCookie(`course-${courseId.value}`)
  await refreshCookie(`unit-${unitId.value}`)

  // Reset game state
  resetGameState()
})

// Set page meta
useHead({
  title: computed(() => {
    if (course.value && unit.value) {
      return `${course.value.title} - ${unit.value.title}`
    }
    return 'English Learning Game'
  }),
  meta: [
    { name: 'description', content: 'Learn English through interactive questions and assessments.' },
  ],
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-blue-900">
    <div class="container mx-auto px-4 py-8">
      <!-- Loading State -->
      <div v-if="courseLoading || unitLoading" class="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4" />
        <p class="text-gray-600 dark:text-gray-300">
          Loading unit...
        </p>
      </div>

      <!-- Error State -->
      <div v-else-if="!course || !unit" class="text-center py-12">
        <div class="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
          <HelpCircle class="w-8 h-8 text-red-600 dark:text-red-400" />
        </div>
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">
          Unit Not Found
        </h3>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
          The requested unit could not be found.
        </p>
        <div class="space-x-4">
          <Button @click="goBack">
            Back to Home
          </Button>
        </div>
      </div>

      <!-- No Questions State -->
      <div v-else-if="questions.length === 0" class="text-center py-12">
        <div class="w-16 h-16 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center mx-auto mb-4">
          <HelpCircle class="w-8 h-8 text-yellow-600 dark:text-yellow-400" />
        </div>
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">
          No Questions Available
        </h3>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
          This unit doesn't have any questions yet.
        </p>
        <div class="space-x-4">
          <Button @click="goBack">
            Back to Home
          </Button>
        </div>
      </div>

      <!-- Game Content -->
      <template v-else-if="currentQuestion">
        <!-- Game Header -->
        <GameNavigation
          :current-stage="courseId"
          :current-level="unitId"
          :current-question-index="currentQuestionIndex"
          :total-questions="totalQuestions"
          :lives="lives"
          :max-lives="maxLives"
          :score="score"
          :progress-percentage="progressPercentage"
          @go-back="goBack"
        />

        <!-- Course and Unit Info -->
        <Card class="mb-6">
          <CardContent class="p-4">
            <div class="text-center">
              <h2 class="text-lg font-semibold text-gray-800 dark:text-white">
                {{ course.title }}
              </h2>
              <p class="text-sm text-gray-600 dark:text-gray-300">
                {{ unit.title }}
              </p>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                {{ unit.description }}
              </p>
            </div>
          </CardContent>
        </Card>

        <!-- Game Content -->
        <main class="max-w-4xl mx-auto">
          <!-- Question Card -->
          <QuestionCard
            :question="currentQuestion"
            :selected-answer="selectedAnswer"
            :answer-selected="answerSelected"
            :show-feedback="showFeedback"
            :is-correct="isCorrect"
            :is-last-question="isLastQuestion"
            class="mb-8"
            @select-answer="selectAnswer"
            @next-question="nextQuestion"
          />
        </main>
      </template>
    </div>
  </div>
</template>
