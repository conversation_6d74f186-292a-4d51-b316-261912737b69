<script setup lang="ts">
import type { UpdateCourseData } from '@/types/course'
import { ArrowLeft, Plus, Save } from 'lucide-vue-next'
import { computed, ref } from 'vue'
import CreateUnitDialog from '@/components/admin/CreateUnitDialog.vue'
import { courseRepository } from '@/repositories/course'

// Meta
definePageMeta({
  layout: 'dashboard',
  title: 'Edit Course',
  middleware: 'auth',
})

// Route params
const route = useRoute()
const courseId = computed(() => Number(route.params.id))

// Fetch course data
const { data: course, pending: loadingCourse, error: courseError, refresh: fetchCourse } = useAsyncData(
  `course-${courseId.value}`,
  () => courseRepository.getCourse(courseId.value),
)

// Set page header configuration
const { setHeaderConfig } = usePageHeader()
setHeaderConfig({
  breadcrumbs: [
    { label: 'Dashboard', href: '/admin/dashboard' },
    { label: 'Courses', href: '/admin/courses' },
    { label: course.value?.title || 'Edit Course', active: true },
  ],
  showAuthStatus: true,
})

// Form state
const form = ref<UpdateCourseData>({
  title: '',
  description: '',
})

// Initialize form when course data loads
watch(course, (newCourse) => {
  if (newCourse) {
    form.value = {
      title: newCourse.title,
      description: newCourse.description,
    }
  }
}, { immediate: true })

const errors = ref<Record<string, string[]>>({})
const loading = ref(false)

// Dialog state
const showCreateUnitDialog = ref(false)

// Router
const router = useRouter()

// Form validation
function validateForm(): boolean {
  errors.value = {}

  if (!form.value.title?.trim()) {
    errors.value.title = ['Course title is required']
  }

  if (!form.value.description?.trim()) {
    errors.value.description = ['Course description is required']
  }

  return Object.keys(errors.value).length === 0
}

// Form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  loading.value = true

  try {
    await courseRepository.updateCourse(courseId.value, form.value)

    // Refresh course data
    await fetchCourse()

    // Show success toast
    console.log('Course updated successfully')
  }
  catch (error: any) {
    console.error('Failed to update course:', error)

    // Handle validation errors from API
    if (error.statusCode === 422 && error.data?.errors) {
      errors.value = error.data.errors
    }
    else {
      // Show generic error toast
      errors.value.general = ['Failed to update course. Please try again.']
    }
  }
  finally {
    loading.value = false
  }
}

// Handle unit creation
function handleUnitCreated(unit: any) {
  console.log('Unit created:', unit)
  // Refresh course data to show new unit
  fetchCourse()
}

// Open create unit dialog with pre-filled course_id
function openCreateUnitDialog() {
  showCreateUnitDialog.value = true
}

// Page meta
useHead({
  title: computed(() => `Edit ${course.value?.title || 'Course'} | English Learning Game Admin`),
  meta: [
    { name: 'description', content: 'Edit course details for the English Learning Game.' },
  ],
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Loading State -->
    <div v-if="loadingCourse" class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
    </div>

    <!-- Error State -->
    <div v-else-if="courseError" class="text-center py-12">
      <p class="text-red-600 dark:text-red-400 mb-4">
        Failed to load course
      </p>
      <Button variant="outline" @click="fetchCourse">
        Try Again
      </Button>
    </div>

    <!-- Course Edit Form -->
    <template v-else-if="course">
      <!-- Page Header -->
      <div class="flex items-center gap-4">
        <div class="flex-1">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Edit Course
          </h1>
          <p class="text-gray-700 dark:text-gray-300 mt-2 font-medium">
            Update course information and manage units
          </p>
        </div>

        <Button
          class="bg-blue-600 hover:bg-blue-700 text-white shadow-sm"
          @click="openCreateUnitDialog"
        >
          <Plus class="w-4 h-4" />
          Add Unit
        </Button>
      </div>

      <!-- Tabs -->
      <Tabs default-value="units" class="space-y-6">
        <TabsList class="grid w-full grid-cols-2">
          <TabsTrigger value="units">
            Course Units
          </TabsTrigger>
          <TabsTrigger value="information">
            Course Information
          </TabsTrigger>
        </TabsList>

        <!-- Course Units Tab -->
        <TabsContent value="units" class="space-y-0">
          <AdminCard
            title="Course Units"
            :description="`${course.units?.length || 0} units in this course`"
            show-header
          >
            <!-- Empty State -->
            <div v-if="!course.units || course.units.length === 0" class="text-center py-8">
              <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-6">
                <p class="text-gray-700 dark:text-gray-300 font-medium mb-4">
                  No units yet
                </p>
                <Button
                  size="sm"
                  class="bg-blue-600 hover:bg-blue-700 text-white"
                  @click="openCreateUnitDialog"
                >
                  <Plus class="w-4 h-4" />
                  Add First Unit
                </Button>
              </div>
            </div>

            <!-- Units List -->
            <div v-else class="space-y-3">
              <div
                v-for="unit in course.units"
                :key="unit.id"
                class="group bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-white dark:hover:bg-gray-800 transition-colors"
              >
                <div class="flex items-start justify-between">
                  <div class="flex-1 min-w-0">
                    <h4 class="font-medium text-gray-900 dark:text-white text-sm mb-2 leading-relaxed">
                      {{ unit.title }}
                    </h4>
                    <p class="text-xs text-gray-600 dark:text-gray-400 mb-2">
                      {{ unit.description }}
                    </p>
                    <div class="flex items-center gap-2">
                      <Badge variant="secondary" class="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300 text-xs">
                        {{ unit.skill_type }}
                      </Badge>
                      <Badge variant="outline" class="border-gray-300 text-gray-700 dark:border-gray-600 dark:text-gray-300 text-xs">
                        {{ unit.difficulty }}
                      </Badge>
                    </div>
                  </div>
                  <Button
                    as-child
                    variant="ghost"
                    size="sm"
                    class="opacity-0 group-hover:opacity-100 transition-opacity"
                  >
                    <NuxtLink :to="`/admin/units/${unit.id}`">
                      Edit
                    </NuxtLink>
                  </Button>
                </div>
              </div>

              <Button
                size="sm"
                variant="outline"
                class="w-full mt-4 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                @click="openCreateUnitDialog"
              >
                <Plus class="w-4 h-4" />
                Add Unit
              </Button>
            </div>
          </AdminCard>
        </TabsContent>

        <!-- Course Information Tab -->
        <TabsContent value="information" class="space-y-0">
          <AdminCard
            title="Course Information"
            description="Update the basic information for this course"
            show-header
          >
            <form class="space-y-4" @submit.prevent="handleSubmit">
              <!-- General Error -->
              <div v-if="errors.general" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                <div class="text-red-800 dark:text-red-200">
                  <ul class="list-disc list-inside space-y-1">
                    <li v-for="error in errors.general" :key="error">
                      {{ error }}
                    </li>
                  </ul>
                </div>
              </div>

              <!-- Course Title -->
              <div class="space-y-2">
                <Label for="title" class="text-gray-900 dark:text-white font-medium">Course Title *</Label>
                <Input
                  id="title"
                  v-model="form.title"
                  placeholder="Enter course title"
                  :class="{ 'border-red-500': errors.title }"
                  class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100"
                  required
                />
                <div v-if="errors.title" class="text-red-600 dark:text-red-400 text-sm">
                  <ul class="list-disc list-inside">
                    <li v-for="error in errors.title" :key="error">
                      {{ error }}
                    </li>
                  </ul>
                </div>
              </div>

              <!-- Course Description -->
              <div class="space-y-2">
                <Label for="description" class="text-gray-900 dark:text-white font-medium">Course Description *</Label>
                <Textarea
                  id="description"
                  v-model="form.description"
                  placeholder="Enter course description"
                  rows="4"
                  :class="{ 'border-red-500': errors.description }"
                  class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100"
                  required
                />
                <div v-if="errors.description" class="text-red-600 dark:text-red-400 text-sm">
                  <ul class="list-disc list-inside">
                    <li v-for="error in errors.description" :key="error">
                      {{ error }}
                    </li>
                  </ul>
                </div>
              </div>

              <!-- Form Actions -->
              <div class="flex items-center gap-4 pt-6">
                <Button
                  type="submit"
                  :disabled="loading"
                  class="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Save class="w-4 h-4" />
                  {{ loading ? 'Saving...' : 'Save Changes' }}
                </Button>

                <Button variant="outline" as-child class="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                  <NuxtLink to="/admin/courses">
                    Cancel
                  </NuxtLink>
                </Button>
              </div>
            </form>
          </AdminCard>
        </TabsContent>
      </Tabs>
    </template>

    <!-- Create Unit Dialog -->
    <CreateUnitDialog
      v-model:open="showCreateUnitDialog"
      :default-course-id="courseId"
      @created="handleUnitCreated"
    />
  </div>
</template>
