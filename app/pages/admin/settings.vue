<template>
  <div class="space-y-6">
    <div>
      <h1 class="text-3xl font-bold tracking-tight">Settings</h1>
      <p class="text-muted-foreground">Manage platform settings and configuration</p>
    </div>

    <div class="grid gap-6 lg:grid-cols-2">
      <AdminCard title="General Settings" description="Basic platform configuration">
        <form @submit.prevent="saveGeneralSettings" class="space-y-4">
          <div class="space-y-2">
            <Label for="platform-name">Platform Name</Label>
            <Input id="platform-name" v-model="settings.platformName" />
          </div>
          
          <div class="space-y-2">
            <Label for="admin-email">Admin Email</Label>
            <Input id="admin-email" type="email" v-model="settings.adminEmail" />
          </div>
          
          <div class="space-y-2">
            <Label for="max-attempts">Max Assessment Attempts</Label>
            <Input id="max-attempts" type="number" v-model.number="settings.maxAttempts" />
          </div>
          
          <div class="flex items-center space-x-2">
            <Checkbox id="maintenance" v-model:checked="settings.maintenanceMode" />
            <Label for="maintenance">Maintenance Mode</Label>
          </div>

          <Button type="submit" class="w-full">
            Save General Settings
          </Button>
        </form>
      </AdminCard>

      <AdminCard title="Notification Settings" description="Configure platform notifications">
        <form @submit.prevent="saveNotificationSettings" class="space-y-4">
          <div class="flex items-center space-x-2">
            <Checkbox id="email-notifications" v-model:checked="notifications.emailEnabled" />
            <Label for="email-notifications">Enable Email Notifications</Label>
          </div>
          
          <div class="flex items-center space-x-2">
            <Checkbox id="user-registration" v-model:checked="notifications.userRegistration" />
            <Label for="user-registration">New User Registration Alerts</Label>
          </div>
          
          <div class="flex items-center space-x-2">
            <Checkbox id="assessment-completion" v-model:checked="notifications.assessmentCompletion" />
            <Label for="assessment-completion">Assessment Completion Alerts</Label>
          </div>
          
          <div class="flex items-center space-x-2">
            <Checkbox id="system-errors" v-model:checked="notifications.systemErrors" />
            <Label for="system-errors">System Error Alerts</Label>
          </div>

          <Button type="submit" class="w-full">
            Save Notification Settings
          </Button>
        </form>
      </AdminCard>

      <AdminCard title="Security Settings" description="Platform security configuration">
        <form @submit.prevent="saveSecuritySettings" class="space-y-4">
          <div class="space-y-2">
            <Label for="session-timeout">Session Timeout (minutes)</Label>
            <Input id="session-timeout" type="number" v-model.number="security.sessionTimeout" />
          </div>
          
          <div class="space-y-2">
            <Label for="password-expiry">Password Expiry (days)</Label>
            <Input id="password-expiry" type="number" v-model.number="security.passwordExpiry" />
          </div>
          
          <div class="flex items-center space-x-2">
            <Checkbox id="two-factor" v-model:checked="security.twoFactorRequired" />
            <Label for="two-factor">Require Two-Factor Authentication</Label>
          </div>
          
          <div class="flex items-center space-x-2">
            <Checkbox id="ip-restrictions" v-model:checked="security.ipRestrictions" />
            <Label for="ip-restrictions">Enable IP Restrictions</Label>
          </div>

          <Button type="submit" class="w-full">
            Save Security Settings
          </Button>
        </form>
      </AdminCard>

      <AdminCard title="Assessment Settings" description="Configure assessment behavior">
        <form @submit.prevent="saveAssessmentSettings" class="space-y-4">
          <div class="space-y-2">
            <Label for="time-limit">Default Time Limit (minutes)</Label>
            <Input id="time-limit" type="number" v-model.number="assessments.defaultTimeLimit" />
          </div>
          
          <div class="space-y-2">
            <Label for="passing-score">Default Passing Score (%)</Label>
            <Input id="passing-score" type="number" v-model.number="assessments.passingScore" min="0" max="100" />
          </div>
          
          <div class="flex items-center space-x-2">
            <Checkbox id="randomize-questions" v-model:checked="assessments.randomizeQuestions" />
            <Label for="randomize-questions">Randomize Questions</Label>
          </div>
          
          <div class="flex items-center space-x-2">
            <Checkbox id="show-correct-answers" v-model:checked="assessments.showCorrectAnswers" />
            <Label for="show-correct-answers">Show Correct Answers After Completion</Label>
          </div>

          <Button type="submit" class="w-full">
            Save Assessment Settings
          </Button>
        </form>
      </AdminCard>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'dashboard',
  middleware: 'admin-auth'
})

// Mock settings data - replace with actual API calls
const settings = ref({
  platformName: 'English Learning Platform',
  adminEmail: '<EMAIL>',
  maxAttempts: 3,
  maintenanceMode: false
})

const notifications = ref({
  emailEnabled: true,
  userRegistration: true,
  assessmentCompletion: false,
  systemErrors: true
})

const security = ref({
  sessionTimeout: 30,
  passwordExpiry: 90,
  twoFactorRequired: false,
  ipRestrictions: false
})

const assessments = ref({
  defaultTimeLimit: 60,
  passingScore: 70,
  randomizeQuestions: true,
  showCorrectAnswers: true
})

function saveGeneralSettings() {
  // Implement API call to save general settings
  console.log('Saving general settings:', settings.value)
}

function saveNotificationSettings() {
  // Implement API call to save notification settings
  console.log('Saving notification settings:', notifications.value)
}

function saveSecuritySettings() {
  // Implement API call to save security settings
  console.log('Saving security settings:', security.value)
}

function saveAssessmentSettings() {
  // Implement API call to save assessment settings
  console.log('Saving assessment settings:', assessments.value)
}
</script>