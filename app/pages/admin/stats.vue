<template>
  <div class="space-y-6">
    <div>
      <h1 class="text-3xl font-bold tracking-tight">Statistics</h1>
      <p class="text-muted-foreground">View platform analytics and user statistics</p>
    </div>

    <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <AdminCard title="Total Users" :value="stats?.totalUsers || 0" icon="Users" />
      <AdminCard title="Active Sessions" :value="stats?.activeSessions || 0" icon="Activity" />
      <AdminCard title="Completed Assessments" :value="stats?.completedAssessments || 0" icon="CheckCircle" />
      <AdminCard title="Average Score" :value="`${stats?.averageScore || 0}%`" icon="TrendingUp" />
    </div>

    <div class="grid gap-6 lg:grid-cols-2">
      <AdminCard title="Recent Activity" description="Latest user activities">
        <div class="space-y-4">
          <div v-for="activity in recentActivities" :key="activity.id" class="flex items-center space-x-4">
            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
            <div class="flex-1">
              <p class="text-sm font-medium">{{ activity.user }}</p>
              <p class="text-xs text-muted-foreground">{{ activity.action }}</p>
            </div>
            <span class="text-xs text-muted-foreground">{{ activity.time }}</span>
          </div>
        </div>
      </AdminCard>

      <AdminCard title="Performance Overview" description="System performance metrics">
        <div class="space-y-4">
          <div class="flex justify-between items-center">
            <span class="text-sm">Response Time</span>
            <span class="text-sm font-medium">{{ stats?.responseTime || 0 }}ms</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm">Uptime</span>
            <span class="text-sm font-medium">{{ stats?.uptime || 0 }}%</span>
          </div>
          <div class="flex justify-between items-center">
            <span class="text-sm">Error Rate</span>
            <span class="text-sm font-medium">{{ stats?.errorRate || 0 }}%</span>
          </div>
        </div>
      </AdminCard>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'dashboard',
  middleware: 'admin-auth'
})

// Mock data - replace with actual API calls
const stats = ref({
  totalUsers: 1250,
  activeSessions: 85,
  completedAssessments: 3420,
  averageScore: 78,
  responseTime: 145,
  uptime: 99.9,
  errorRate: 0.1
})

const recentActivities = ref([
  { id: 1, user: 'John Doe', action: 'Completed Unit 3 Assessment', time: '2 min ago' },
  { id: 2, user: 'Jane Smith', action: 'Started Course: Advanced English', time: '5 min ago' },
  { id: 3, user: 'Mike Wilson', action: 'Achieved 95% score in Grammar Test', time: '12 min ago' },
  { id: 4, user: 'Sarah Johnson', action: 'Registered for platform', time: '18 min ago' },
])
</script>