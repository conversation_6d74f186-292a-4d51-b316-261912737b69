<script setup lang="ts">
import type { Assessmentable, AssessmentAiGapFillSentence, AssessmentMultiSelect, UpdateUnitData } from '@/types/course'
import { AssessmentType } from '#shared/constants'
import { Plus, Save, Trash2, Upload } from 'lucide-vue-next'
import { assessmentRepository } from '@/repositories/assessment'
import { unitRepository } from '@/repositories/unit'
import { getAssessmentType, isAiGapFillSentenceAssessment, isMultiSelectAssessment } from '@/utils/assessment'
import AssessmentAiGapFillSentenceEditDialog from '~/components/admin/assessments/AssessmentAiGapFillSentence/AssessmentAiGapFillSentenceEditDialog.vue'
import AssessmentAiGapFillSentenceViewCard from '~/components/admin/assessments/AssessmentAiGapFillSentence/AssessmentAiGapFillSentenceViewCard.vue'
import MultiselectEditDialog from '~/components/admin/assessments/MultiSelect/MultiselectEditDialog.vue'
import MultiselectViewCard from '~/components/admin/assessments/MultiSelect/MultiselectViewCard.vue'
import CSVImportDialog from '~/components/admin/CSVImportDialog.vue'

// Meta
definePageMeta({
  layout: 'dashboard',
  title: 'Edit Unit',
  middleware: 'auth',
})

// Route params
const route = useRoute()
const unitId = computed(() => Number(route.params.id))

// Fetch unit data
const { data: unit, pending: loadingUnit, error: unitError, refresh: fetchUnit } = useAsyncData(
  `unit-${unitId.value}`,
  () => unitRepository.getUnit(unitId.value),
)

const assessments = computed(() => unit.value?.assessments || [])

// Set page header configuration
const { setHeaderConfig } = usePageHeader()
setHeaderConfig({
  breadcrumbs: [
    { label: 'Dashboard', href: '/admin/dashboard' },
    { label: 'Units', href: '/admin/units' },
    { label: unit.value?.title || 'Edit Unit', active: true },
  ],
  showAuthStatus: true,
})

// Form state - Initialize as reactive with proper hydration handling
const form = ref<UpdateUnitData>({
  title: '',
  description: '',
  skill_type: undefined,
  difficulty: undefined,
  unit_order: 1,
})

const errors = ref<Record<string, string[]>>({})
const loading = ref(false)

// Initialize form when unit data loads - prevent hydration mismatch
watch(unit, (newUnit) => {
  if (newUnit) {
    // Use nextTick to ensure DOM is updated after data fetch
    nextTick(() => {
      form.value = {
        title: newUnit.title,
        description: newUnit.description,
        skill_type: newUnit.skill_type,
        difficulty: newUnit.difficulty,
        unit_order: newUnit.unit_order,
      }
    })
  }
}, { immediate: true })

// Skill type options
const skillTypeOptions = [
  { value: 'vocabulary', label: 'Vocabulary' },
  { value: 'grammar', label: 'Grammar' },
  { value: 'listening', label: 'Listening' },
  { value: 'reading', label: 'Reading' },
  { value: 'speaking', label: 'Speaking' },
  { value: 'writing', label: 'Writing' },
]

// Difficulty options
const difficultyOptions = [
  { value: 'beginner', label: 'Beginner' },
  { value: 'intermediate', label: 'Intermediate' },
  { value: 'advanced', label: 'Advanced' },
]

// Dialog states
const csvImportDialog = ref(false)

// Type-specific dialog states
const multiselectCreateDialog = ref(false)
const multiselectEditDialog = ref(false)
const selectedMultiselectAssessment = ref<AssessmentMultiSelect | null>(null)

const aiGapFillCreateDialog = ref(false)
const aiGapFillEditDialog = ref(false)
const selectedAiGapFillAssessment = ref<AssessmentAiGapFillSentence | null>(null)

// Form validation
function validateForm(): boolean {
  errors.value = {}

  if (!form.value.title?.trim()) {
    errors.value.title = ['Title is required']
  }

  if (!form.value.description?.trim()) {
    errors.value.description = ['Description is required']
  }

  return Object.keys(errors.value).length === 0
}

// Form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  const { success, error: _error } = useToast()
  loading.value = true

  try {
    await unitRepository.updateUnit(unitId.value, form.value)
    await fetchUnit()

    success('Unit updated successfully', {
      description: 'Your changes have been saved',
      duration: 4000,
    })
  }
  catch (error: any) {
    console.error('Failed to update unit:', error)

    // Handle validation errors from API
    if (error.statusCode === 422 && error.data?.errors) {
      errors.value = error.data.errors
      _error('Validation errors found', {
        description: 'Please check the form and fix any errors',
        duration: 5000,
      })
    }
    else {
      errors.value.general = ['Failed to update unit. Please try again.']
      _error('Failed to update unit', {
        description: error.message || 'Please try again later',
        duration: 5000,
      })
    }
  }
  finally {
    loading.value = false
  }
}

// Assessment actions
function handleCreateAssessment() {
  if (!unit.value)
    return

  // Use the unit's unit_type to determine which dialog to open
  if (unit.value.unit_type === AssessmentType.MULTIPLE_SELECT) {
    multiselectCreateDialog.value = true
  }
  else if (unit.value.unit_type === AssessmentType.AI_GAP_FILL_SENTENCE) {
    aiGapFillCreateDialog.value = true
  }
}

function handleEditMultiselectAssessment(assessment: AssessmentMultiSelect) {
  selectedMultiselectAssessment.value = assessment
  multiselectEditDialog.value = true
}

function handleEditAiGapFillAssessment(assessment: AssessmentAiGapFillSentence) {
  selectedAiGapFillAssessment.value = assessment
  aiGapFillEditDialog.value = true
}

async function handleDeleteAssessment(assessment: Assessmentable) {
  const { confirm } = useModal()
  const { success, error: _error } = useToast()

  const confirmed = await confirm({
    title: 'Delete Assessment',
    description: `Are you sure you want to delete this assessment? This action cannot be undone.`,
    variant: 'destructive',
  })

  if (confirmed) {
    try {
      const assessmentType = getAssessmentType(assessment)
      await assessmentRepository.deleteAssessment(assessmentType, assessment.id)
      await fetchUnit()

      success('Assessment deleted successfully', {
        description: 'The assessment has been permanently removed from this unit',
        duration: 4000,
      })
    }
    catch (error: any) {
      console.error('Failed to delete assessment:', error)

      _error('Failed to delete assessment', {
        description: error.message || 'Please try again later',
        duration: 5000,
      })
    }
  }
}

function handleImportCSV() {
  csvImportDialog.value = true
}

async function handleClearAllAssessments() {
  const { confirm } = useModal()
  const { success, error: _error } = useToast()

  const confirmed = await confirm({
    title: 'Clear All Assessments',
    description: `Are you sure you want to delete all ${assessments.value.length} assessments from this unit? This action cannot be undone.`,
    variant: 'destructive',
  })

  if (confirmed) {
    try {
      const result = await assessmentRepository.clearUnitAssessments(unitId.value)
      await fetchUnit()

      success(`Cleared ${result.deleted_count} assessments`, {
        description: 'All assessments have been permanently removed from this unit',
        duration: 4000,
      })
    }
    catch (error: any) {
      console.error('Failed to clear assessments:', error)

      _error('Failed to clear assessments', {
        description: error.message || 'Please try again later',
        duration: 5000,
      })
    }
  }
}

// Page meta
useHead({
  title: computed(() => `Edit ${unit.value?.title || 'Unit'} | English Learning Game Admin`),
  meta: [
    { name: 'description', content: 'Edit unit details and manage assessments for the English Learning Game.' },
  ],
})
</script>

<template>
  <div class="p-6 space-y-6">
    <!-- Loading State -->
    <div v-if="loadingUnit" class="flex items-center justify-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600" />
    </div>

    <!-- Error State -->
    <div v-else-if="unitError" class="text-center py-12">
      <div class="bg-red-50 dark:bg-red-900/20 rounded-lg p-6">
        <p class="text-red-700 dark:text-red-400 font-medium mb-3">
          Failed to load unit
        </p>
        <Button variant="outline" class="border-red-300 text-red-700 hover:bg-red-50" @click="fetchUnit">
          Try Again
        </Button>
      </div>
    </div>

    <!-- Unit Edit Form -->
    <template v-else-if="unit">
      <!-- Page Header -->
      <div class="flex items-center gap-4">
        <div class="flex-1">
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
            Edit Unit
          </h1>
          <p class="text-gray-700 dark:text-gray-300 mt-2 font-medium">
            Update unit information and manage assessments
          </p>
        </div>

        <div class="flex items-center gap-3">
          <Button variant="outline" class="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700" @click="handleImportCSV">
            <Upload class="w-4 h-4" />
            Import CSV
          </Button>

          <Button class="bg-blue-600 hover:bg-blue-700 text-white shadow-sm" @click="handleCreateAssessment">
            <Plus class="w-4 h-4" />
            Add Assessment
          </Button>
        </div>
      </div>

      <!-- Tabs -->
      <Tabs default-value="assessments" class="space-y-6">
        <TabsList class="grid w-full grid-cols-2">
          <TabsTrigger value="assessments">
            Unit Assessments
          </TabsTrigger>
          <TabsTrigger value="information">
            Unit Information
          </TabsTrigger>
        </TabsList>

        <!-- Unit Assessments Tab -->
        <TabsContent value="assessments" class="space-y-0">
          <AdminCard
            title="Unit Assessments"
            :description="`${assessments.length} assessments in this unit`"
            show-header
          >
            <template #header-actions>
              <div class="flex items-center gap-2">
                <Button
                  v-if="assessments.length > 0"
                  variant="outline"
                  size="sm"
                  class="border-red-300 text-red-700 hover:text-red-600 hover:bg-red-50 dark:border-red-700 dark:text-red-400 dark:hover:bg-red-900/20"
                  @click="handleClearAllAssessments"
                >
                  <Trash2 class="w-4 h-4" />
                  Clear All
                </Button>
              </div>
            </template>
            <!-- Empty State -->
            <div v-if="assessments.length === 0" class="text-center py-8">
              <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-6">
                <p class="text-gray-700 dark:text-gray-300 font-medium mb-4">
                  No assessments yet
                </p>
                <Button size="sm" class="bg-blue-600 hover:bg-blue-700 text-white" @click="handleCreateAssessment">
                  <Plus class="w-4 h-4" />
                  Add First Assessment
                </Button>
              </div>
            </div>

            <!-- Assessments List -->
            <div v-else class="space-y-3">
              <template v-for="assessment in assessments" :key="assessment.id">
                <!-- Multiple Select Assessment -->
                <MultiselectViewCard
                  v-if="isMultiSelectAssessment(assessment)"
                  :assessment="assessment"
                  @edit="handleEditMultiselectAssessment"
                  @delete="handleDeleteAssessment"
                />

                <!-- AI Gap Fill Sentence Assessment -->
                <AssessmentAiGapFillSentenceViewCard
                  v-else-if="isAiGapFillSentenceAssessment(assessment)"
                  :assessment="assessment"
                  @edit="handleEditAiGapFillAssessment"
                  @delete="handleDeleteAssessment"
                />
              </template>

              <Button size="sm" variant="outline" class="w-full mt-4 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700" @click="handleCreateAssessment">
                <Plus class="w-4 h-4" />
                Add Assessment
              </Button>
            </div>
          </AdminCard>
        </TabsContent>

        <!-- Unit Information Tab -->
        <TabsContent value="information" class="space-y-0">
          <AdminCard
            title="Unit Information"
            description="Update the basic information for this unit"
            show-header
          >
            <form class="space-y-4" @submit.prevent="handleSubmit">
              <!-- General Error -->
              <div v-if="errors.general" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
                <div class="text-red-800 dark:text-red-200">
                  <ul class="list-disc list-inside space-y-1">
                    <li v-for="error in errors.general" :key="error">
                      {{ error }}
                    </li>
                  </ul>
                </div>
              </div>

              <!-- Unit Title -->
              <div class="space-y-2">
                <Label for="title" class="text-gray-900 dark:text-white font-medium">Unit Title *</Label>
                <Input
                  id="title"
                  v-model="form.title"
                  placeholder="Enter unit title"
                  :class="{ 'border-red-500': errors.title }"
                  class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100"
                  required
                />
                <div v-if="errors.title" class="text-red-600 dark:text-red-400 text-sm">
                  <ul class="list-disc list-inside">
                    <li v-for="error in errors.title" :key="error">
                      {{ error }}
                    </li>
                  </ul>
                </div>
              </div>

              <!-- Unit Description -->
              <div class="space-y-2">
                <Label for="description" class="text-gray-900 dark:text-white font-medium">Unit Description *</Label>
                <Textarea
                  id="description"
                  v-model="form.description"
                  placeholder="Enter unit description"
                  rows="4"
                  :class="{ 'border-red-500': errors.description }"
                  class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100"
                  required
                />
                <div v-if="errors.description" class="text-red-600 dark:text-red-400 text-sm">
                  <ul class="list-disc list-inside">
                    <li v-for="error in errors.description" :key="error">
                      {{ error }}
                    </li>
                  </ul>
                </div>
              </div>

              <!-- Skill Type -->
              <div class="space-y-2">
                <Label for="skill_type" class="text-gray-900 dark:text-white font-medium">Skill Type *</Label>
                <ClientOnly>
                  <Select v-model="form.skill_type">
                    <SelectTrigger class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100">
                      <SelectValue placeholder="Select skill type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem
                        v-for="option in skillTypeOptions"
                        :key="option.value"
                        :value="option.value"
                      >
                        {{ option.label }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <template #fallback>
                    <div class="h-10 bg-gray-100 dark:bg-gray-800 rounded-md animate-pulse" />
                  </template>
                </ClientOnly>
              </div>

              <!-- Difficulty -->
              <div class="space-y-2">
                <Label for="difficulty" class="text-gray-900 dark:text-white font-medium">Difficulty *</Label>
                <ClientOnly>
                  <Select v-model="form.difficulty">
                    <SelectTrigger class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 text-gray-900 dark:text-gray-100">
                      <SelectValue placeholder="Select difficulty" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem
                        v-for="option in difficultyOptions"
                        :key="option.value"
                        :value="option.value"
                      >
                        {{ option.label }}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <template #fallback>
                    <div class="h-10 bg-gray-100 dark:bg-gray-800 rounded-md animate-pulse" />
                  </template>
                </ClientOnly>
              </div>

              <!-- Unit Order -->
              <div class="space-y-2">
                <Label for="unit_order" class="text-gray-900 dark:text-white font-medium">Unit Order *</Label>
                <Input
                  id="unit_order"
                  v-model.number="form.unit_order"
                  type="number"
                  min="1"
                  placeholder="Enter unit order"
                  class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100"
                  required
                />
              </div>

              <!-- Form Actions -->
              <div class="flex items-center gap-4 pt-6">
                <Button
                  type="submit"
                  :disabled="loading"
                  class="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  <Save class="w-4 h-4" />
                  {{ loading ? 'Saving...' : 'Save Changes' }}
                </Button>

                <Button variant="outline" as-child class="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                  <NuxtLink to="/admin/units">
                    Cancel
                  </NuxtLink>
                </Button>
              </div>
            </form>
          </AdminCard>
        </TabsContent>
      </Tabs>
    </template>

    <!-- Multiple Select Assessment Dialogs -->
    <MultiselectEditDialog
      v-model:open="multiselectCreateDialog"
      :assessment="null"
      :unit-id="unitId"
      @updated="fetchUnit"
    />

    <MultiselectEditDialog
      v-model:open="multiselectEditDialog"
      :assessment="selectedMultiselectAssessment"
      @updated="fetchUnit"
    />

    <!-- AI Gap Fill Sentence Assessment Dialogs -->
    <AssessmentAiGapFillSentenceEditDialog
      v-model:open="aiGapFillCreateDialog"
      :assessment="null"
      :unit-id="unitId"
      @updated="fetchUnit"
    />

    <AssessmentAiGapFillSentenceEditDialog
      v-model:open="aiGapFillEditDialog"
      :assessment="selectedAiGapFillAssessment"
      @updated="fetchUnit"
    />

    <!-- CSV Import Dialog -->
    <CSVImportDialog
      v-if="unit"
      v-model:open="csvImportDialog"
      :unit="unit"
      @imported="fetchUnit"
    />
  </div>
</template>
