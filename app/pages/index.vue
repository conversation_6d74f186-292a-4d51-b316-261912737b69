<script setup lang="ts">
import type { Course, CourseWithStats } from '@/types/course'
import { Award, BarChart3, BookO<PERSON>, HelpCircle, Play, Zap } from 'lucide-vue-next'
import { useModal } from '@/composables/useModal'
import { courseRepository } from '@/repositories/course'
import { unitRepository } from '@/repositories/unit'
import { useProgressStore } from '@/stores/progress'

// Modal system
const modal = useModal()

// Progress store
const progressStore = useProgressStore()

// Fetch courses with statistics
const { data: coursesData, pending: coursesLoading, error: coursesError } = await useAsyncData(
  'public-courses-with-stats',
  () => courseRepository.getPublicCoursesWithStats(),
)

const courses = computed(() => coursesData.value || [])

// Get course progress for display
function getCourseProgressPercentage(courseId: number): number {
  const progress = progressStore.getCourseProgress(courseId)
  if (!progress || progress.totalUnits === 0)
    return 0
  return Math.round((progress.unitsCompleted / progress.totalUnits) * 100)
}

function isCourseStarted(courseId: number): boolean {
  const progress = progressStore.getCourseProgress(courseId)
  return progress ? progress.unitsCompleted > 0 : false
}

function isCourseCompleted(courseId: number): boolean {
  return progressStore.isCourseCompleted(courseId)
}

// Start game with selected course
async function startCourse(course: CourseWithStats) {
  try {
    // Track course access
    progressStore.accessCourse(course.id, course.title)

    // Get units for the course to start with the first unit
    const units = course.units || []

    if (units.length === 0) {
      await modal.alert({
        title: 'No Units Available',
        description: 'This course doesn\'t have any units yet. Please try another course.',
        variant: 'info',
      })
      return
    }

    // Sort units by unit_order and find the first incomplete unit or start from the beginning
    const sortedUnits = units.sort((a, b) => a.unit_order - b.unit_order)
    let targetUnit = sortedUnits[0] // Default to first unit

    // Find the first incomplete unit
    for (const unit of sortedUnits) {
      if (!progressStore.isUnitCompleted(unit.id)) {
        targetUnit = unit
        break
      }
    }

    // Navigate to the target unit
    if (targetUnit) {
      navigateTo(`/game/course/${course.id}/unit/${targetUnit.id}`)
    }
  }
  catch (error) {
    console.error('Error starting course:', error)
    await modal.alert({
      title: 'Error',
      description: 'Failed to start the course. Please try again.',
      variant: 'destructive',
    })
  }
}

function viewStats() {
  // Navigate to statistics page
  navigateTo('/stats')
}

// Set page meta
useHead({
  title: 'English Learning Game - Choose Your Course',
  meta: [
    { name: 'description', content: 'Choose from available English learning courses. Each course contains multiple units to help you improve your language skills.' },
  ],
})

function getTotalQuestion(course: Course) {
  return course.units?.reduce((total, unit) => total + (unit.assessments?.length || 0), 0) || 0
}
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-blue-900">
    <div class="container mx-auto px-4 py-8">
      <!-- Header -->
      <header class="text-center mb-12">
        <h1 class="text-4xl md:text-5xl font-bold text-gray-800 dark:text-white mb-4">
          🎯 English Learning Courses
        </h1>
        <p class="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
          Choose from our available courses to start your English learning journey. Each course contains multiple units with interactive lessons.
        </p>
      </header>

      <!-- Loading State -->
      <div v-if="coursesLoading" class="text-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4" />
        <p class="text-gray-600 dark:text-gray-300">
          Loading courses...
        </p>
      </div>

      <!-- Error State -->
      <div v-else-if="coursesError" class="text-center py-12">
        <div class="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
          <HelpCircle class="w-8 h-8 text-red-600 dark:text-red-400" />
        </div>
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">
          Failed to Load Courses
        </h3>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
          There was an error loading the courses. Please try again.
        </p>
        <Button @click="$router.go(0)">
          Retry
        </Button>
      </div>

      <!-- Empty State -->
      <div v-else-if="courses.length === 0" class="text-center py-12">
        <div class="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-full flex items-center justify-center mx-auto mb-4">
          <BookOpen class="w-8 h-8 text-gray-600 dark:text-gray-400" />
        </div>
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">
          No Courses Available
        </h3>
        <p class="text-gray-600 dark:text-gray-300 mb-4">
          There are no courses available at the moment. Please check back later.
        </p>
      </div>

      <!-- Course Cards -->
      <div v-else class="space-y-8">
        <!-- Course Grid -->
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          <Card
            v-for="course in courses"
            :key="course.id"
            class="hover:shadow-xl transition-all duration-300 cursor-pointer group"
            @click="startCourse(course)"
          >
            <CardContent class="p-6">
              <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <BookOpen class="w-8 h-8 text-white" />
                </div>
                <CardTitle class="mb-2 text-left">
                  {{ course.title }}
                </CardTitle>
                <CardDescription class="mb-4 text-left line-clamp-3">
                  {{ course.description }}
                </CardDescription>

                <!-- Course Stats -->
                <div class="flex items-center justify-between text-sm text-gray-600 dark:text-gray-300 mb-4">
                  <div class="flex items-center gap-1">
                    <Zap class="w-4 h-4" />
                    <span>{{ course.units?.length || 0 }} units</span>
                  </div>
                  <div class="flex items-center gap-1">
                    <HelpCircle class="w-4 h-4" />
                    <span>{{ getTotalQuestion(course) }} questions</span>
                  </div>
                </div>

                <!-- Progress Bar -->
                <div v-if="isCourseStarted(course.id)" class="mb-4">
                  <div class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-300 mb-1">
                    <span>Progress</span>
                    <span>{{ getCourseProgressPercentage(course.id) }}%</span>
                  </div>
                  <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div
                      class="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
                      :style="{ width: `${getCourseProgressPercentage(course.id)}%` }"
                    />
                  </div>
                </div>

                <Button
                  class="w-full"
                  size="lg"
                  :variant="isCourseCompleted(course.id) ? 'outline' : 'default'"
                >
                  <Play class="w-4 h-4" />
                  {{ isCourseCompleted(course.id) ? 'Review Course' : isCourseStarted(course.id) ? 'Continue' : 'Start Course' }}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        <!-- Quick Actions -->
        <div class="grid md:grid-cols-2 gap-6">
          <!-- Statistics Card -->
          <Card class="hover:shadow-xl transition-all duration-300 cursor-pointer group">
            <CardContent class="p-6">
              <div class="text-center">
                <div class="w-16 h-16 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <BarChart3 class="w-8 h-8 text-purple-600 dark:text-purple-400" />
                </div>
                <CardTitle class="mb-2">
                  Statistics
                </CardTitle>
                <CardDescription class="mb-4">
                  View your learning progress
                </CardDescription>
                <Button
                  variant="secondary"
                  class="w-full"
                  size="lg"
                  @click="viewStats"
                >
                  View Stats
                </Button>
              </div>
            </CardContent>
          </Card>

          <!-- Achievement Card -->
          <Card class="hover:shadow-xl transition-all duration-300 cursor-pointer group">
            <CardContent class="p-6">
              <div class="text-center">
                <div class="w-16 h-16 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform">
                  <Award class="w-8 h-8 text-yellow-600 dark:text-yellow-400" />
                </div>
                <CardTitle class="mb-2">
                  Achievements
                </CardTitle>
                <CardDescription class="mb-4">
                  Track your accomplishments
                </CardDescription>
                <Button
                  variant="outline"
                  class="w-full"
                  size="lg"
                  disabled
                >
                  Coming Soon
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <!-- Game Features Preview -->
      <Card>
        <CardHeader>
          <CardTitle class="text-center">
            Game Features
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid md:grid-cols-3 gap-6">
            <div class="text-center">
              <div class="w-12 h-12 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Zap class="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
              </div>
              <h3 class="font-semibold text-card-foreground mb-2">
                Multiple Stages
              </h3>
              <CardDescription>Progress through increasingly challenging levels</CardDescription>
            </div>
            <div class="text-center">
              <div class="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                <HelpCircle class="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <h3 class="font-semibold text-card-foreground mb-2">
                Interactive Questions
              </h3>
              <CardDescription>Answer multiple choice questions to advance</CardDescription>
            </div>
            <div class="text-center">
              <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mx-auto mb-3">
                <Award class="w-6 h-6 text-green-600 dark:text-green-400" />
              </div>
              <h3 class="font-semibold text-card-foreground mb-2">
                Achievement System
              </h3>
              <CardDescription>Earn rewards and celebrate victories</CardDescription>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  </div>
</template>
