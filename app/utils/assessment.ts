import type {
  Assessmentable,
  AssessmentAiGapFillSentence,
  AssessmentMultiSelect,
} from '@/types/course'
import { AssessmentType } from '#shared/constants'

/**
 * Type guard to check if an assessment is a MultiSelect assessment
 */
export function isMultiSelectAssessment(assessment: Assessmentable): assessment is AssessmentMultiSelect {
  return 'answer_list' in assessment
    && 'correct_answer_indexes' in assessment
    && 'explanations' in assessment
}

/**
 * Type guard to check if an assessment is an AI Gap Fill Sentence assessment
 */
export function isAiGapFillSentenceAssessment(assessment: Assessmentable): assessment is AssessmentAiGapFillSentence {
  return 'context' in assessment
    && 'fill_position' in assessment
}

/**
 * Get the assessment type from an assessment object
 */
export function getAssessmentType(assessment: Assessmentable): AssessmentType {
  if (isMultiSelectAssessment(assessment)) {
    return AssessmentType.MULTIPLE_SELECT
  }
  if (isAiGapFillSentenceAssessment(assessment)) {
    return AssessmentType.AI_GAP_FILL_SENTENCE
  }
  throw new Error('Unknown assessment type')
}

/**
 * Get a human-readable label for an assessment type
 */
export function getAssessmentTypeLabel(type: AssessmentType): string {
  switch (type) {
    case AssessmentType.MULTIPLE_SELECT:
      return 'Multiple Select'
    case AssessmentType.AI_GAP_FILL_SENTENCE:
      return 'AI Gap Fill Sentence'
    default:
      return 'Unknown'
  }
}

/**
 * Get available assessment types for creation
 */
export function getAvailableAssessmentTypes(): Array<{ value: AssessmentType, label: string }> {
  return [
    { value: AssessmentType.MULTIPLE_SELECT, label: 'Multiple Select' },
    { value: AssessmentType.AI_GAP_FILL_SENTENCE, label: 'AI Gap Fill Sentence' },
  ]
}

/**
 * Validate assessment data based on type
 */
export function validateAssessmentData(type: AssessmentType, data: any): { isValid: boolean, errors: string[] } {
  const errors: string[] = []

  if (!data.question?.trim()) {
    errors.push('Question is required')
  }

  if (type === AssessmentType.MULTIPLE_SELECT) {
    if (!data.answer_list || !Array.isArray(data.answer_list) || data.answer_list.length < 2) {
      errors.push('At least 2 answer options are required')
    }

    if (data.answer_list && data.answer_list.some((answer: string) => !answer?.trim())) {
      errors.push('All answer options must be filled')
    }

    if (!data.correct_answer_indexes || !Array.isArray(data.correct_answer_indexes) || data.correct_answer_indexes.length === 0) {
      errors.push('At least one correct answer must be selected')
    }
  }

  if (type === AssessmentType.AI_GAP_FILL_SENTENCE) {
    if (!data.context?.trim()) {
      errors.push('Context is required')
    }

    if (!data.fill_position || !Array.isArray(data.fill_position) || data.fill_position.length === 0) {
      errors.push('At least one fill position is required')
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}
