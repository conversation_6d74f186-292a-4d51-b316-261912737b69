import type {
  Assessmentable,
  AssessmentAiGapFillSentence,
  AssessmentMultiSelect,
} from '@/types/course'
import { AssessmentType } from '#shared/constants'

/**
 * Type guard to check if an assessment is a MultiSelect assessment
 */
export function isMultiSelectAssessment(assessment: Assessmentable): assessment is AssessmentMultiSelect {
  return 'answer_list' in assessment
    && 'correct_answer_indexes' in assessment
    && 'explanations' in assessment
}

/**
 * Type guard to check if an assessment is an AI Gap Fill Sentence assessment
 */
export function isAiGapFillSentenceAssessment(assessment: Assessmentable): assessment is AssessmentAiGapFillSentence {
  return 'context' in assessment
    && 'fill_position' in assessment
}

/**
 * Get the assessment type from an assessment object
 */
export function getAssessmentType(assessment: Assessmentable): AssessmentType {
  if (isMultiSelectAssessment(assessment)) {
    return AssessmentType.MULTIPLE_SELECT
  }
  if (isAiGapFillSentenceAssessment(assessment)) {
    return AssessmentType.AI_GAP_FILL_SENTENCE
  }
  throw new Error('Unknown assessment type')
}

/**
 * Get a human-readable label for an assessment type
 */
export function getAssessmentTypeLabel(type: AssessmentType): string {
  switch (type) {
    case AssessmentType.MULTIPLE_SELECT:
      return 'Multiple Select'
    case AssessmentType.AI_GAP_FILL_SENTENCE:
      return 'AI Gap Fill Sentence'
    default:
      return 'Unknown'
  }
}

/**
 * Get available assessment types for creation
 */
export function getAvailableAssessmentTypes(): Array<{ value: AssessmentType, label: string }> {
  return [
    { value: AssessmentType.MULTIPLE_SELECT, label: 'Multiple Select' },
    { value: AssessmentType.AI_GAP_FILL_SENTENCE, label: 'AI Gap Fill Sentence' },
  ]
}

/**
 * Parse text with gap markers (any sequence of underscores) into structured parts
 * @param text - Text containing gap markers
 * @returns Array of text parts with gap indicators
 */
export function parseGapText(text: string): Array<{ text: string, isGap: boolean }> {
  const parts: { text: string, isGap: boolean }[] = []

  // Use regex to split by any sequence of underscores (2 or more)
  const regex = /_{2,}/g
  let lastIndex = 0
  let match

  while ((match = regex.exec(text)) !== null) {
    // Add text before the gap if it exists
    if (match.index > lastIndex) {
      const textBefore = text.slice(lastIndex, match.index)
      if (textBefore.length > 0) {
        parts.push({ text: textBefore, isGap: false })
      }
    }

    // Add the gap
    parts.push({ text: '', isGap: true })

    lastIndex = regex.lastIndex
  }

  // Add remaining text after the last gap
  if (lastIndex < text.length) {
    const remainingText = text.slice(lastIndex)
    if (remainingText.length > 0) {
      parts.push({ text: remainingText, isGap: false })
    }
  }

  return parts
}

/**
 * Count the number of gaps in a text string
 * @param text - Text containing gap markers (any sequence of 2+ underscores)
 * @returns Number of gaps found
 */
export function countGaps(text: string): number {
  return (text.match(/_{2,}/g) || []).length
}

/**
 * Fill gaps in text with provided answers
 * @param text - Original text with gap markers (any sequence of 2+ underscores)
 * @param answers - Array of answers to fill gaps with
 * @returns Text with gaps filled
 */
export function fillGaps(text: string, answers: (string | undefined)[]): string {
  let result = text
  answers.forEach((answer) => {
    result = result.replace(/_{2,}/, (answer || '').trim())
  })
  return result
}

/**
 * Validate assessment data based on type
 */
export function validateAssessmentData(type: AssessmentType, data: any): { isValid: boolean, errors: string[] } {
  const errors: string[] = []

  if (!data.question?.trim()) {
    errors.push('Question is required')
  }

  if (type === AssessmentType.MULTIPLE_SELECT) {
    if (!data.answer_list || !Array.isArray(data.answer_list) || data.answer_list.length < 2) {
      errors.push('At least 2 answer options are required')
    }

    if (data.answer_list && data.answer_list.some((answer: string) => !answer?.trim())) {
      errors.push('All answer options must be filled')
    }

    if (!data.correct_answer_indexes || !Array.isArray(data.correct_answer_indexes) || data.correct_answer_indexes.length === 0) {
      errors.push('At least one correct answer must be selected')
    }
  }

  if (type === AssessmentType.AI_GAP_FILL_SENTENCE) {
    if (!data.context?.trim()) {
      errors.push('Context is required')
    }

    if (!data.fill_position || !Array.isArray(data.fill_position) || data.fill_position.length === 0) {
      errors.push('At least one fill position is required')
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}
