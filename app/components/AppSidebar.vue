<script setup lang="ts">
import { BarChart3, BookOpen, GraduationCap, Home, MessageSquare, Settings, TestTube, Trophy, Users } from 'lucide-vue-next'

// Navigation items for admin dashboard
const navigationItems = [
  {
    title: 'Dashboard',
    icon: Home,
    href: '/admin/dashboard',
    active: false,
  },
  {
    title: 'Courses',
    icon: BookOpen,
    href: '/admin/courses',
    active: false,
  },
  {
    title: 'Units',
    icon: GraduationCap,
    href: '/admin/units',
    active: false,
  },
  {
    title: 'Statistics',
    icon: BarChart3,
    href: '/admin/stats',
    active: false,
  },
  {
    title: 'Users',
    icon: Users,
    href: '/admin/users',
    active: false,
  },
  {
    title: 'AI Prompts',
    icon: MessageSquare,
    href: '/admin/ai-prompts',
    active: false,
  },
  {
    title: 'AI Testing',
    icon: TestTube,
    href: '/admin/ai-assessments/test',
    active: false,
  },
  {
    title: 'Settings',
    icon: Settings,
    href: '/admin/settings',
    active: false,
  },
]
</script>

<template>
  <Sidebar>
    <SidebarHeader>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" as-child>
            <NuxtLink to="/admin/dashboard">
              <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                <BookOpen class="size-4" />
              </div>
              <div class="grid flex-1 text-left text-sm leading-tight">
                <span class="truncate font-semibold">English Game</span>
                <span class="truncate text-xs">Admin Dashboard</span>
              </div>
            </NuxtLink>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarHeader>

    <SidebarContent>
      <SidebarGroup>
        <SidebarGroupLabel>Navigation</SidebarGroupLabel>
        <SidebarGroupContent>
          <SidebarMenu>
            <SidebarMenuItem v-for="item in navigationItems" :key="item.title">
              <SidebarMenuButton as-child :is-active="item.active">
                <NuxtLink :to="item.href">
                  <component :is="item.icon" class="size-4" />
                  <span>{{ item.title }}</span>
                </NuxtLink>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </SidebarContent>

    <SidebarFooter>
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton size="lg" as-child>
            <NuxtLink to="/">
              <div class="flex aspect-square size-8 items-center justify-center rounded-lg bg-muted">
                <Home class="size-4" />
              </div>
              <div class="grid flex-1 text-left text-sm leading-tight">
                <span class="truncate font-semibold">Back to Game</span>
                <span class="truncate text-xs">Return to main site</span>
              </div>
            </NuxtLink>
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    </SidebarFooter>
  </Sidebar>
</template>
