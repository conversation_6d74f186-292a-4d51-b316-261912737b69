<script setup lang="ts">
import type { AssessmentMultiSelect } from '@/types/course'
import { CheckCircle, ChevronRight, Flag, XCircle } from 'lucide-vue-next'

interface QuestionOption {
  text: string
  isCorrect: boolean
}

interface ProcessedQuestion {
  id: number
  question: string
  options: QuestionOption[]
  explanation: string
  correctAnswers: number[]
}

interface Props {
  assessment: AssessmentMultiSelect
  selectedAnswer: number | null
  answerSelected: boolean
  showFeedback: boolean
  isCorrect: boolean
  isLastQuestion: boolean
}

interface Emits {
  'select-answer': [index: number]
  'next-question': []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Convert assessment to question format
const question = computed<ProcessedQuestion>(() => ({
  id: props.assessment.id,
  question: props.assessment.question,
  options: props.assessment.answer_list.map((answer: string, answerIndex: number) => ({
    text: answer,
    isCorrect: props.assessment.correct_answer_indexes.includes(answerIndex),
  })),
  explanation: props.assessment.explanations?.[0] || 'No explanation provided.',
  correctAnswers: props.assessment.correct_answer_indexes,
}))

function getOptionClass(index: number) {
  if (!props.answerSelected) {
    return 'bg-background border-border hover:bg-accent hover:text-accent-foreground'
  }

  if (props.selectedAnswer === index) {
    return props.isCorrect
      ? 'bg-green-100 dark:bg-green-900 border-green-500 text-green-800 dark:text-green-200'
      : 'bg-red-100 dark:bg-red-900 border-red-500 text-red-800 dark:text-red-200'
  }

  if (question.value.options[index]?.isCorrect) {
    return 'bg-green-100 dark:bg-green-900 border-green-500 text-green-800 dark:text-green-200'
  }

  return 'bg-muted border-border text-muted-foreground'
}

function getOptionBadgeClass(index: number) {
  if (!props.answerSelected) {
    return 'bg-muted text-muted-foreground'
  }

  if (props.selectedAnswer === index) {
    return props.isCorrect
      ? 'bg-green-500 text-white'
      : 'bg-red-500 text-white'
  }

  if (question.value.options[index]?.isCorrect) {
    return 'bg-green-500 text-white'
  }

  return 'bg-muted text-muted-foreground'
}

function handleSelectAnswer(index: number) {
  emit('select-answer', index)
}

function handleNextQuestion() {
  emit('next-question')
}
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle class="text-center text-2xl md:text-3xl">
        {{ question.question }}
      </CardTitle>
    </CardHeader>

    <CardContent>
      <!-- Answer Options -->
      <div class="grid gap-4 md:grid-cols-2 mb-6">
        <button
          v-for="(option, index) in question.options"
          :key="index"
          :disabled="answerSelected"
          :class="getOptionClass(index)"
          class="p-4 rounded-lg text-left font-medium transition-all duration-300 border-2 disabled:cursor-not-allowed hover:scale-[1.02] active:scale-[0.98]"
          @click="handleSelectAnswer(index)"
        >
          <div class="flex items-center gap-3">
            <div
              class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold transition-all"
              :class="getOptionBadgeClass(index)"
            >
              {{ String.fromCharCode(65 + index) }}
            </div>
            <span>{{ option.text }}</span>
          </div>
        </button>
      </div>

      <!-- Feedback Message -->
      <div v-if="showFeedback" class="mb-6">
        <Alert :class="isCorrect ? 'border-green-500 bg-green-50 dark:bg-green-950' : 'border-red-500 bg-red-50 dark:bg-red-950'">
          <div class="flex items-center gap-2">
            <CheckCircle v-if="isCorrect" class="w-5 h-5 text-green-600 dark:text-green-400" />
            <XCircle v-else class="w-5 h-5 text-red-600 dark:text-red-400" />
            <AlertTitle :class="isCorrect ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'">
              {{ isCorrect ? 'Correct! 🎉' : 'Incorrect 😔' }}
            </AlertTitle>
          </div>
          <AlertDescription v-if="question.explanation" :class="isCorrect ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'">
            {{ question.explanation }}
          </AlertDescription>
        </Alert>
      </div>

      <!-- Next Button -->
      <div v-if="showFeedback" class="text-center">
        <Button
          size="lg"
          class="px-8 flex items-center gap-2"
          @click="handleNextQuestion"
        >
          <span>{{ isLastQuestion ? 'Complete Level' : 'Next Question' }}</span>
          <Flag v-if="isLastQuestion" class="w-4 h-4" />
          <ChevronRight v-else class="w-4 h-4" />
        </Button>
      </div>
    </CardContent>
  </Card>
</template>
