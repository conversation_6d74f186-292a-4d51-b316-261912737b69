<script setup lang="ts">
import type { AssessmentAiGapFillSentence } from '@/types/course'
import { CheckCircle, ChevronRight, Flag, XCircle } from 'lucide-vue-next'

interface Props {
  assessment: AssessmentAiGapFillSentence
  userAnswers: string[]
  answerSubmitted: boolean
  showFeedback: boolean
  isCorrect: boolean
  isLastQuestion: boolean
}

interface Emits {
  'update-answer': [index: number, value: string]
  'submit-answer': []
  'next-question': []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Parse the context to identify words and gaps
const contextWords = computed(() => {
  if (!props.assessment.context)
    return []

  return props.assessment.context.split(' ').map((word, index) => ({
    word,
    index,
    isGap: props.assessment.fill_position?.includes(index) || false,
  }))
})

// Get gap positions for input management
const gapPositions = computed(() => {
  return props.assessment.fill_position || []
})

// Check if all gaps are filled
const allGapsFilled = computed(() => {
  return gapPositions.value.every((_, index) => {
    return props.userAnswers[index]?.trim().length > 0
  })
})

function handleAnswerUpdate(gapIndex: number, value: string) {
  emit('update-answer', gapIndex, value)
}

function handleSubmitAnswer() {
  if (allGapsFilled.value && !props.answerSubmitted) {
    emit('submit-answer')
  }
}

function handleNextQuestion() {
  emit('next-question')
}

// Handle Enter key press to submit
function handleKeyPress(event: KeyboardEvent, gapIndex: number) {
  if (event.key === 'Enter') {
    event.preventDefault()
    if (gapIndex === gapPositions.value.length - 1 && allGapsFilled.value) {
      handleSubmitAnswer()
    }
    else {
      // Focus next input
      const nextInput = document.querySelector(`input[data-gap-index="${gapIndex + 1}"]`) as HTMLInputElement
      if (nextInput) {
        nextInput.focus()
      }
    }
  }
}
</script>

<template>
  <Card>
    <CardHeader>
      <CardTitle class="text-center text-2xl md:text-3xl">
        {{ assessment.question }}
      </CardTitle>
    </CardHeader>

    <CardContent>
      <!-- Context with Gap Fill Inputs -->
      <div class="mb-6">
        <div class="bg-gray-50 dark:bg-gray-900 rounded-lg p-6 text-lg leading-relaxed">
          <template v-for="(wordObj, index) in contextWords" :key="index">
            <!-- Regular word -->
            <span v-if="!wordObj.isGap" class="text-gray-800 dark:text-gray-200">
              {{ wordObj.word }}
            </span>

            <!-- Gap input -->
            <span v-else class="inline-block mx-1">
              <input
                :data-gap-index="gapPositions.indexOf(wordObj.index)"
                :value="userAnswers[gapPositions.indexOf(wordObj.index)] || ''"
                :disabled="answerSubmitted"
                :class="{
                  'border-green-500 bg-green-50 dark:bg-green-900': showFeedback && isCorrect,
                  'border-red-500 bg-red-50 dark:bg-red-900': showFeedback && !isCorrect,
                  'border-gray-300 dark:border-gray-600': !showFeedback,
                }"
                class="inline-block min-w-[100px] px-2 py-1 border-2 rounded text-center font-medium text-gray-800 dark:text-gray-200 bg-white dark:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:cursor-not-allowed"
                placeholder="___"
                @input="handleAnswerUpdate(gapPositions.indexOf(wordObj.index), ($event.target as HTMLInputElement).value)"
                @keypress="handleKeyPress($event, gapPositions.indexOf(wordObj.index))"
              >
            </span>

            <!-- Add space after word (except last) -->
            <span v-if="index < contextWords.length - 1" />
          </template>
        </div>
      </div>

      <!-- Instructions -->
      <div v-if="!answerSubmitted" class="mb-6 text-center">
        <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
          Fill in the missing words in the sentence above. Press Enter to move to the next gap or submit your answer.
        </p>

        <!-- Submit Button -->
        <Button
          :disabled="!allGapsFilled"
          size="lg"
          class="px-8"
          @click="handleSubmitAnswer"
        >
          Submit Answer
        </Button>
      </div>

      <!-- Feedback Message -->
      <div v-if="showFeedback" class="mb-6">
        <Alert :class="isCorrect ? 'border-green-500 bg-green-50 dark:bg-green-950' : 'border-red-500 bg-red-50 dark:bg-red-950'">
          <div class="flex items-center gap-2">
            <CheckCircle v-if="isCorrect" class="w-5 h-5 text-green-600 dark:text-green-400" />
            <XCircle v-else class="w-5 h-5 text-red-600 dark:text-red-400" />
            <AlertTitle :class="isCorrect ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'">
              {{ isCorrect ? 'Correct! 🎉' : 'Incorrect 😔' }}
            </AlertTitle>
          </div>
          <AlertDescription :class="isCorrect ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'">
            {{ isCorrect ? 'Great job! You filled in all the gaps correctly.' : 'Some of your answers were incorrect. Keep practicing!' }}
          </AlertDescription>
        </Alert>
      </div>

      <!-- Next Button -->
      <div v-if="showFeedback" class="text-center">
        <Button
          size="lg"
          class="px-8 flex items-center gap-2"
          @click="handleNextQuestion"
        >
          <span>{{ isLastQuestion ? 'Complete Level' : 'Next Question' }}</span>
          <Flag v-if="isLastQuestion" class="w-4 h-4" />
          <ChevronRight v-else class="w-4 h-4" />
        </Button>
      </div>
    </CardContent>
  </Card>
</template>
