<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

const { toast, success, error, warning, info, loading, promise, dismiss } = useToast()

// Demo functions for different toast types
const showBasicToast = () => {
  toast('Hello World!', {
    description: 'This is a basic toast message.',
    duration: 3000,
  })
}

const showSuccessToast = () => {
  success('Success!', {
    description: 'Your action was completed successfully.',
  })
}

const showErrorToast = () => {
  error('Error occurred!', {
    description: 'Something went wrong. Please try again.',
  })
}

const showWarningToast = () => {
  warning('Warning!', {
    description: 'Please check your input and try again.',
  })
}

const showInfoToast = () => {
  info('Information', {
    description: 'Here is some useful information for you.',
  })
}

const showLoadingToast = () => {
  const toastId = loading('Loading...', {
    description: 'Please wait while we process your request.',
  })
  
  // Dismiss after 3 seconds
  setTimeout(() => {
    dismiss(toastId)
    success('Completed!', {
      description: 'The operation finished successfully.',
    })
  }, 3000)
}

const showToastWithAction = () => {
  toast('Action Required', {
    description: 'Click the button to perform an action.',
    action: {
      label: 'Undo',
      onClick: () => {
        info('Action performed!', {
          description: 'You clicked the action button.',
        })
      },
    },
  })
}

const showPromiseToast = () => {
  const mockPromise = new Promise((resolve, reject) => {
    setTimeout(() => {
      if (Math.random() > 0.5) {
        resolve('Success data')
      } else {
        reject(new Error('Something went wrong'))
      }
    }, 2000)
  })

  promise(mockPromise, {
    loading: 'Processing...',
    success: (data) => `Success: ${data}`,
    error: (error) => `Error: ${error.message}`,
  })
}

// Game-specific toast examples
const showGameToasts = () => {
  // Example: Level completion
  success('Level Complete!', {
    description: 'You earned 100 points!',
    duration: 4000,
  })
  
  setTimeout(() => {
    // Example: Achievement unlocked
    info('Achievement Unlocked!', {
      description: '🏆 First Level Master',
      duration: 5000,
    })
  }, 1000)
}

const showAssessmentToasts = () => {
  // Example: Assessment saved
  success('Assessment Saved', {
    description: 'Your progress has been saved automatically.',
  })
  
  setTimeout(() => {
    // Example: Time warning
    warning('Time Running Out!', {
      description: 'You have 2 minutes remaining.',
      duration: 6000,
    })
  }, 1500)
}
</script>

<template>
  <Card class="w-full max-w-2xl mx-auto">
    <CardHeader>
      <CardTitle>Toast Notifications Demo</CardTitle>
      <CardDescription>
        Try different types of toast notifications using vue-sonner
      </CardDescription>
    </CardHeader>
    <CardContent class="space-y-4">
      <div class="grid grid-cols-2 gap-3">
        <Button @click="showBasicToast" variant="outline">
          Basic Toast
        </Button>
        
        <Button @click="showSuccessToast" variant="outline" class="text-green-600">
          Success Toast
        </Button>
        
        <Button @click="showErrorToast" variant="outline" class="text-red-600">
          Error Toast
        </Button>
        
        <Button @click="showWarningToast" variant="outline" class="text-yellow-600">
          Warning Toast
        </Button>
        
        <Button @click="showInfoToast" variant="outline" class="text-blue-600">
          Info Toast
        </Button>
        
        <Button @click="showLoadingToast" variant="outline">
          Loading Toast
        </Button>
        
        <Button @click="showToastWithAction" variant="outline">
          Toast with Action
        </Button>
        
        <Button @click="showPromiseToast" variant="outline">
          Promise Toast
        </Button>
      </div>
      
      <div class="border-t pt-4">
        <h3 class="text-lg font-semibold mb-3">Game-Specific Examples</h3>
        <div class="grid grid-cols-2 gap-3">
          <Button @click="showGameToasts" variant="default">
            Game Progress Toasts
          </Button>
          
          <Button @click="showAssessmentToasts" variant="default">
            Assessment Toasts
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>
</template>
