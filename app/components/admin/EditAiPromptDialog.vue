<script setup lang="ts">
import type { AiPrompt, UpdateAiPromptData } from '@/types/ai-prompt'
import { Save } from 'lucide-vue-next'
import { aiPromptRepository } from '@/repositories/ai-prompt'

interface Props {
  open: boolean
  aiPrompt: AiPrompt | null
}

interface Emits {
  'update:open': [value: boolean]
  'updated': []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Local state
const isOpen = computed({
  get: () => props.open,
  set: value => emit('update:open', value),
})

const form = ref({
  prompt: '',
  item_replace_pattern: '',
  default_context: '',
})

const errors = ref<Record<string, string[]>>({})
const loading = ref(false)

// Initialize form when AI prompt changes or dialog opens
watch(() => props.open, (open) => {
  if (open && props.aiPrompt) {
    form.value = {
      prompt: props.aiPrompt.prompt,
      item_replace_pattern: props.aiPrompt.item_replace_pattern,
      default_context: props.aiPrompt.default_context,
    }
    errors.value = {}
  }
}, { immediate: true })

// Reset form when dialog closes
watch(isOpen, (open) => {
  if (!open) {
    errors.value = {}
  }
})

// Form validation
function validateForm(): boolean {
  errors.value = {}

  if (!form.value.prompt?.trim()) {
    errors.value.prompt = ['Prompt template is required']
  }

  if (!form.value.item_replace_pattern?.trim()) {
    errors.value.item_replace_pattern = ['Item replace pattern is required']
  }

  if (!form.value.default_context?.trim()) {
    errors.value.default_context = ['Default context is required']
  }

  return Object.keys(errors.value).length === 0
}

// Form submission
async function handleSubmit() {
  if (!validateForm() || !props.aiPrompt) {
    return
  }

  const { success, error: showError } = useToast()
  loading.value = true

  try {
    const updateData: UpdateAiPromptData = {
      prompt: form.value.prompt,
      item_replace_pattern: form.value.item_replace_pattern,
      default_context: form.value.default_context,
    }

    await aiPromptRepository.updateAiPrompt(props.aiPrompt.type, updateData)

    success('AI Prompt updated successfully', {
      description: 'Your changes have been saved',
      duration: 4000,
    })

    emit('updated')
    isOpen.value = false
  }
  catch (error: any) {
    console.error('Failed to update AI prompt:', error)

    // Handle validation errors from API
    if (error.statusCode === 422 && error.data?.errors) {
      errors.value = error.data.errors
      showError('Validation errors found', {
        description: 'Please check the form and fix any errors',
        duration: 5000,
      })
    }
    else {
      errors.value.general = ['Failed to update AI prompt. Please try again.']
      showError('Failed to update AI prompt', {
        description: error.message || 'Please try again later',
        duration: 5000,
      })
    }
  }
  finally {
    loading.value = false
  }
}

function handleCancel() {
  isOpen.value = false
}
</script>

<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle class="text-xl font-semibold text-gray-900 dark:text-white">
          Edit AI Prompt: {{ aiPrompt?.type }}
        </DialogTitle>
        <DialogDescription class="text-gray-600 dark:text-gray-400">
          Configure the AI prompt template, item replacement pattern, and default context for assessment generation.
        </DialogDescription>
      </DialogHeader>

      <form class="space-y-6" @submit.prevent="handleSubmit">
        <!-- General Error -->
        <div v-if="errors.general" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div class="text-red-800 dark:text-red-200">
            <ul class="list-disc list-inside space-y-1">
              <li v-for="error in errors.general" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Prompt Template -->
        <div class="space-y-2">
          <Label for="prompt" class="text-gray-900 dark:text-white font-medium">
            Prompt Template *
          </Label>
          <div class="text-sm text-gray-600 dark:text-gray-400 mb-2">
            Use {variable_names} for dynamic content. Common variables: {context}, {question}, {answers}, {id}
          </div>
          <Textarea
            id="prompt"
            v-model="form.prompt"
            placeholder="Enter the AI prompt template with {variable_placeholders}"
            rows="8"
            :class="{ 'border-red-500': errors.prompt }"
            class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100 font-mono text-sm"
            required
          />
          <div v-if="errors.prompt" class="text-red-600 dark:text-red-400 text-sm">
            <ul class="list-disc list-inside">
              <li v-for="error in errors.prompt" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Item Replace Pattern -->
        <div class="space-y-2">
          <Label for="item_replace_pattern" class="text-gray-900 dark:text-white font-medium">
            Item Replace Pattern *
          </Label>
          <div class="text-sm text-gray-600 dark:text-gray-400 mb-2">
            Pattern used to format individual items. Example: "ID {id}: Answer '{answer}' - Context: {context}"
          </div>
          <Input
            id="item_replace_pattern"
            v-model="form.item_replace_pattern"
            placeholder="ID {id}: Answer '{answer}' - Context: {context}"
            :class="{ 'border-red-500': errors.item_replace_pattern }"
            class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100 font-mono"
            required
          />
          <div v-if="errors.item_replace_pattern" class="text-red-600 dark:text-red-400 text-sm">
            <ul class="list-disc list-inside">
              <li v-for="error in errors.item_replace_pattern" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Default Context -->
        <div class="space-y-2">
          <Label for="default_context" class="text-gray-900 dark:text-white font-medium">
            Default Context *
          </Label>
          <div class="text-sm text-gray-600 dark:text-gray-400 mb-2">
            Default context used when no specific context is provided
          </div>
          <Textarea
            id="default_context"
            v-model="form.default_context"
            placeholder="Enter default context for when none is provided"
            rows="4"
            :class="{ 'border-red-500': errors.default_context }"
            class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100"
            required
          />
          <div v-if="errors.default_context" class="text-red-600 dark:text-red-400 text-sm">
            <ul class="list-disc list-inside">
              <li v-for="error in errors.default_context" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Dialog Actions -->
        <DialogFooter class="flex items-center gap-3">
          <Button
            type="button"
            variant="outline"
            :disabled="loading"
            class="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            @click="handleCancel"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            :disabled="loading"
            class="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Save class="w-4 h-4" />
            {{ loading ? 'Saving...' : 'Save Changes' }}
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>
