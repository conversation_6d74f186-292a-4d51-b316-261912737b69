<script setup lang="ts">
import type { AssessmentAiGapFillSentence, CreateAiGapFillSentenceAssessmentData, UpdateAiGapFillSentenceAssessmentData } from '@/types/course'
import { AssessmentType } from '#shared/constants'
import { Save } from 'lucide-vue-next'
import { assessmentRepository } from '@/repositories/assessment'

interface Props {
  open: boolean
  assessment: AssessmentAiGapFillSentence | null
  unitId?: number
}

interface Emits {
  'update:open': [value: boolean]
  'updated': []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Local state
const isOpen = computed({
  get: () => props.open,
  set: value => emit('update:open', value),
})

const form = ref({
  question: '',
  context: '',
})

const errors = ref<Record<string, string[]>>({})
const loading = ref(false)

// Computed to determine if we're in create mode
const isCreateMode = computed(() => !props.assessment)

// Initialize form when assessment changes or dialog opens
watch(() => props.open, (open) => {
  if (open) {
    if (props.assessment) {
      // Edit mode
      form.value = {
        question: props.assessment.question,
        context: props.assessment.context,
      }
    }
    else {
      // Create mode
      form.value = {
        question: '',
        context: '',
      }
    }
    errors.value = {}
  }
}, { immediate: true })

// Reset form when dialog closes
watch(isOpen, (open) => {
  if (!open) {
    errors.value = {}
  }
})

// Form validation
function validateForm(): boolean {
  errors.value = {}

  if (!form.value.question?.trim()) {
    errors.value.question = ['Question is required']
  }

  if (!form.value.context?.trim()) {
    errors.value.context = ['Context is required']
  }

  return Object.keys(errors.value).length === 0
}

// Form submission
async function handleSubmit() {
  if (!validateForm()) {
    return
  }

  const { success, error: showError } = useToast()
  loading.value = true

  try {
    if (isCreateMode.value) {
      // Create mode
      if (!props.unitId) {
        errors.value.general = ['Unit ID is required for creating assessments']
        return
      }

      const createData: CreateAiGapFillSentenceAssessmentData & { type: AssessmentType.AI_GAP_FILL_SENTENCE } = {
        type: AssessmentType.AI_GAP_FILL_SENTENCE,
        question: form.value.question,
        context: form.value.context,
        unit_attachments: [{ unit_id: props.unitId }],
      }

      await assessmentRepository.createAssessment(createData)

      success('Assessment created successfully', {
        description: 'New AI gap fill sentence assessment has been added to the unit',
        duration: 4000,
      })
    }
    else {
      // Edit mode
      if (!props.assessment) {
        return
      }

      const updateData: UpdateAiGapFillSentenceAssessmentData = {
        question: form.value.question,
        context: form.value.context,
      }

      await assessmentRepository.updateAssessment(AssessmentType.AI_GAP_FILL_SENTENCE, props.assessment.id, updateData)

      success('Assessment updated successfully', {
        description: 'Your changes have been saved',
        duration: 4000,
      })
    }

    emit('updated')
    isOpen.value = false
  }
  catch (error: any) {
    console.error(`Failed to ${isCreateMode.value ? 'create' : 'update'} assessment:`, error)

    // Handle validation errors from API
    if (error.statusCode === 422 && error.data?.errors) {
      errors.value = error.data.errors
      showError('Validation errors found', {
        description: 'Please check the form and fix any errors',
        duration: 5000,
      })
    }
    else {
      // Use specific error message if available, otherwise fall back to generic message
      const specificError = error.data?.error || error.message || `Failed to ${isCreateMode.value ? 'create' : 'update'} assessment. Please try again.`
      errors.value.general = [specificError]
      showError(`Failed to ${isCreateMode.value ? 'create' : 'update'} assessment`, {
        description: specificError,
        duration: 5000,
      })
    }
  }
  finally {
    loading.value = false
  }
}

function handleCancel() {
  isOpen.value = false
}
</script>

<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="max-w-3xl max-h-[90vh] overflow-y-auto">
      <DialogHeader>
        <DialogTitle class="text-xl font-semibold text-gray-900 dark:text-white">
          {{ isCreateMode ? 'Create AI Gap Fill Sentence Assessment' : 'Edit AI Gap Fill Sentence Assessment' }}
        </DialogTitle>
        <DialogDescription class="text-gray-600 dark:text-gray-400">
          {{ isCreateMode ? 'Create a new AI gap fill sentence assessment with context and fill positions.' : 'Update the assessment question, context, and fill positions.' }}
        </DialogDescription>
      </DialogHeader>

      <form class="space-y-6" @submit.prevent="handleSubmit">
        <!-- General Error -->
        <div v-if="errors.general" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4">
          <div class="text-red-800 dark:text-red-200">
            <ul class="list-disc list-inside space-y-1">
              <li v-for="error in errors.general" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Question -->
        <div class="space-y-2">
          <Label for="question" class="text-gray-900 dark:text-white font-medium">
            Question *
          </Label>
          <Textarea
            id="question"
            v-model="form.question"
            placeholder="Enter the assessment question (e.g., 'Fill in the missing words in the sentence below')"
            rows="2"
            :class="{ 'border-red-500': errors.question }"
            class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100"
            required
          />
          <div v-if="errors.question" class="text-red-600 dark:text-red-400 text-sm">
            <ul class="list-disc list-inside">
              <li v-for="error in errors.question" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Context -->
        <div class="space-y-2">
          <Label for="context" class="text-gray-900 dark:text-white font-medium">
            Context *
          </Label>
          <Textarea
            id="context"
            v-model="form.context"
            placeholder="Enter the sentence or paragraph with words that students need to fill in"
            rows="4"
            :class="{ 'border-red-500': errors.context }"
            class="bg-gray-50 dark:bg-gray-900 border-gray-300 dark:border-gray-600 focus:border-blue-500 dark:focus:border-blue-400 text-gray-900 dark:text-gray-100"
            required
          />
          <div v-if="errors.context" class="text-red-600 dark:text-red-400 text-sm">
            <ul class="list-disc list-inside">
              <li v-for="error in errors.context" :key="error">
                {{ error }}
              </li>
            </ul>
          </div>
        </div>

        <!-- Dialog Actions -->
        <DialogFooter class="flex items-center gap-3">
          <Button
            type="button"
            variant="outline"
            :disabled="loading"
            class="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
            @click="handleCancel"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            :disabled="loading"
            class="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Save class="w-4 h-4" />
            {{ loading ? (isCreateMode ? 'Creating...' : 'Saving...') : (isCreateMode ? 'Create Assessment' : 'Save Changes') }}
          </Button>
        </DialogFooter>
      </form>
    </DialogContent>
  </Dialog>
</template>
