<script setup lang="ts">
import type { AssessmentAiGapFillSentence } from '@/types/course'
import { Edit, MoreHorizontal, Trash2 } from 'lucide-vue-next'

interface Props {
  assessment: AssessmentAiGapFillSentence
}

interface Emits {
  edit: [assessment: AssessmentAiGapFillSentence]
  delete: [assessment: AssessmentAiGapFillSentence]
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

function handleEdit() {
  emit('edit', props.assessment)
}

function handleDelete() {
  emit('delete', props.assessment)
}
</script>

<template>
  <div class="group bg-gray-50 dark:bg-gray-900/50 border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-white dark:hover:bg-gray-800 transition-colors">
    <div class="flex items-start justify-between">
      <div class="flex-1 min-w-0">
        <!-- Assessment Type Badge -->
        <div class="flex items-center gap-2 mb-2">
          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400">
            AI Gap Fill Sentence
          </span>
        </div>

        <!-- Question -->
        <h4 class="font-medium text-gray-900 dark:text-white text-sm mb-2 leading-relaxed">
          {{ assessment.question }}
        </h4>

        <!-- Context with gaps highlighted -->
        <div class="bg-gray-100 dark:bg-gray-800 rounded-md p-3 mb-2">
          {{ assessment.context }}
        </div>
      </div>

      <!-- Actions Menu -->
      <DropdownMenu>
        <DropdownMenuTrigger as-child>
          <Button variant="ghost" size="sm" class="opacity-0 group-hover:opacity-100 transition-opacity">
            <MoreHorizontal class="w-4 h-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" class="w-40">
          <DropdownMenuItem @click="handleEdit">
            <Edit class="w-4 h-4" />
            Edit
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem
            class="text-red-600 focus:text-red-600 focus:bg-red-50 dark:focus:bg-red-900/20"
            @click="handleDelete"
          >
            <Trash2 class="w-4 h-4" />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  </div>
</template>
