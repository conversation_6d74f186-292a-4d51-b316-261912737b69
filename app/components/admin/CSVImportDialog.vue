<script setup lang="ts">
import type { ImportResponse, Unit, ValidationResponse } from '@/types/course'
import { AlertCircle, CheckCircle, Download, FileText, Upload, X } from 'lucide-vue-next'
import { computed, ref } from 'vue'
import { csvImportRepository } from '@/repositories/csvImport'

interface Props {
  open: boolean
  unit: Unit
}

interface Emits {
  'update:open': [value: boolean]
  'imported': []
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Local state
const isOpen = computed({
  get: () => props.open,
  set: value => emit('update:open', value),
})

const selectedFile = ref<File | null>(null)
const dragOver = ref(false)
const loading = ref(false)
const validating = ref(false)
const validationResult = ref<ValidationResponse | null>(null)
const importResult = ref<ImportResponse | null>(null)
const errors = ref<string[]>([])

// File drop handlers
function handleDrop(event: DragEvent) {
  event.preventDefault()
  dragOver.value = false

  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    handleFileSelect(files[0] as File)
  }
}

function handleDragOver(event: DragEvent) {
  event.preventDefault()
  dragOver.value = true
}

function handleDragLeave() {
  dragOver.value = false
}

function handleFileInput(event: Event) {
  const target = event.target as HTMLInputElement
  if (target.files && target.files.length > 0) {
    handleFileSelect(target.files[0] as File)
  }
}

function handleFileSelect(file: File) {
  // Validate file type
  const validation = csvImportRepository.validateFile(file)
  if (!validation.isValid) {
    errors.value = [validation.error || 'Invalid file type']
    return
  }

  selectedFile.value = file
  errors.value = []
  validationResult.value = null
  importResult.value = null
}

function removeFile() {
  selectedFile.value = null
  validationResult.value = null
  importResult.value = null
  errors.value = []
}

// Import CSV
async function importCSV() {
  if (!selectedFile.value)
    return

  loading.value = true
  errors.value = []

  try {
    importResult.value = await csvImportRepository.importCSV(selectedFile.value, props.unit.id)
    emit('imported')

    // Show success message and close dialog after a delay
    setTimeout(() => {
      isOpen.value = false
    }, 2000)
  }
  catch (error: any) {
    console.error('Failed to import CSV:', error)
    errors.value = ['Failed to import CSV file. Please try again.']
  }
  finally {
    loading.value = false
  }
}

// Download template
async function downloadTemplate() {
  try {
    const blob = await csvImportRepository.downloadTemplate(props.unit.unit_type)
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'assessment_template.csv'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }
  catch (error: any) {
    console.error('Failed to download template:', error)
    errors.value = ['Failed to download template. Please try again.']
  }
}

function handleClose() {
  selectedFile.value = null
  validationResult.value = null
  importResult.value = null
  errors.value = []
  isOpen.value = false
}

// Format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0)
    return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`
}
</script>

<template>
  <Dialog v-model:open="isOpen">
    <DialogContent class="max-w-lg">
      <DialogHeader>
        <DialogTitle class="text-xl font-semibold text-gray-900 dark:text-white">
          Import Assessments from CSV
        </DialogTitle>
        <DialogDescription class="text-gray-600 dark:text-gray-400">
          Upload a CSV file to import multiple assessments into this unit.
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-6">
        <!-- Download Template -->
        <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div class="flex items-start gap-3">
            <Download class="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
            <div class="flex-1">
              <h4 class="font-medium text-blue-900 dark:text-blue-100 mb-1">
                Need a template?
              </h4>
              <p class="text-sm text-blue-800 dark:text-blue-200 mb-3">
                Download our CSV template to see the required format.
              </p>
              <Button
                variant="outline"
                size="sm"
                class="border-blue-300 text-blue-700 hover:bg-blue-100 dark:border-blue-600 dark:text-blue-300 dark:hover:bg-blue-900/30"
                @click="downloadTemplate"
              >
                <Download class="w-4 h-4" />
                Download Template
              </Button>
            </div>
          </div>
        </div>

        <!-- File Upload Area -->
        <div
          class="border-2 border-dashed rounded-lg p-8 text-center transition-colors"
          :class="{
            'border-blue-300 bg-blue-50 dark:border-blue-600 dark:bg-blue-900/20': dragOver,
            'border-gray-300 dark:border-gray-600': !dragOver && !selectedFile,
            'border-green-300 bg-green-50 dark:border-green-600 dark:bg-green-900/20': selectedFile,
          }"
          @drop="handleDrop"
          @dragover="handleDragOver"
          @dragleave="handleDragLeave"
        >
          <div v-if="!selectedFile">
            <Upload class="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Drop your CSV file here
            </h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
              or click to browse files
            </p>
            <input
              id="csv-file-input"
              type="file"
              accept=".csv,.txt"
              class="hidden"
              @change="handleFileInput"
            >
            <Button as-child variant="outline" class="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
              <label for="csv-file-input" class="cursor-pointer">
                <FileText class="w-4 h-4" />
                Choose File
              </label>
            </Button>
          </div>

          <div v-else class="space-y-4">
            <div class="flex items-center justify-center gap-3">
              <FileText class="w-8 h-8 text-green-600 dark:text-green-400" />
              <div class="text-left">
                <p class="font-medium text-gray-900 dark:text-white">
                  {{ selectedFile.name }}
                </p>
                <p class="text-sm text-gray-600 dark:text-gray-400">
                  {{ formatFileSize(selectedFile.size) }}
                </p>
              </div>
              <Button
                variant="ghost"
                size="sm"
                class="text-gray-500 hover:text-red-600"
                @click="removeFile"
              >
                <X class="w-4 h-4" />
              </Button>
            </div>

            <!-- Validation Results -->
            <div v-if="validationResult" class="text-left">
              <div v-if="validationResult.is_valid" class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                <div class="flex items-center gap-2 mb-2">
                  <CheckCircle class="w-5 h-5 text-green-600 dark:text-green-400" />
                  <span class="font-medium text-green-900 dark:text-green-100">
                    Validation Successful
                  </span>
                </div>
                <p class="text-sm text-green-800 dark:text-green-200">
                  Found {{ validationResult.preview?.length || 0 }} valid assessments ready to import.
                </p>
              </div>

              <div v-else class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                <div class="flex items-center gap-2 mb-2">
                  <AlertCircle class="w-5 h-5 text-red-600 dark:text-red-400" />
                  <span class="font-medium text-red-900 dark:text-red-100">
                    Validation Failed
                  </span>
                </div>
                <div v-if="validationResult.errors?.length" class="text-sm text-red-800 dark:text-red-200">
                  <ul class="list-disc list-inside space-y-1">
                    <li v-for="error in validationResult.errors" :key="error">
                      {{ error }}
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Import Results -->
            <div v-if="importResult" class="text-left">
              <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
                <div class="flex items-center gap-2 mb-2">
                  <CheckCircle class="w-5 h-5 text-green-600 dark:text-green-400" />
                  <span class="font-medium text-green-900 dark:text-green-100">
                    Import Successful
                  </span>
                </div>
                <p class="text-sm text-green-800 dark:text-green-200">
                  Successfully imported {{ importResult.summary?.assessments_created || 0 }} assessments.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Errors -->
        <div v-if="errors.length > 0" class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div class="flex items-start gap-3">
            <AlertCircle class="w-5 h-5 text-red-600 dark:text-red-400 mt-0.5" />
            <div>
              <h4 class="font-medium text-red-900 dark:text-red-100 mb-1">
                Error
              </h4>
              <ul class="text-sm text-red-800 dark:text-red-200 list-disc list-inside space-y-1">
                <li v-for="error in errors" :key="error">
                  {{ error }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <DialogFooter class="flex items-center gap-3">
        <Button
          variant="outline"
          :disabled="loading || validating"
          class="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
          @click="handleClose"
        >
          {{ importResult ? 'Close' : 'Cancel' }}
        </Button>

        <div v-if="selectedFile && !importResult" class="flex items-center gap-2">
          <Button
            :disabled="loading"
            class="bg-blue-600 hover:bg-blue-700 text-white"
            @click="importCSV"
          >
            <Upload class="w-4 h-4" />
            {{ loading ? 'Importing...' : 'Import' }}
          </Button>
        </div>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>
