import { defineStore } from 'pinia'

export interface UnitProgress {
  unitId: number
  courseId: number
  completed: boolean
  score: number
  totalQuestions: number
  correctAnswers: number
  completedAt?: string
  timeSpent?: number // in seconds
}

export interface CourseProgress {
  courseId: number
  courseTitle: string
  unitsCompleted: number
  totalUnits: number
  totalScore: number
  totalQuestions: number
  totalCorrectAnswers: number
  completed: boolean
  completedAt?: string
  lastAccessedAt: string
}

export interface GameStats {
  totalCoursesCompleted: number
  totalUnitsCompleted: number
  totalQuestionsAnswered: number
  totalCorrectAnswers: number
  averageScore: number
  totalTimeSpent: number // in seconds
  streakDays: number
  lastPlayedDate?: string
}

export const useProgressStore = defineStore('progress', {
  state: () => ({
    unitProgress: [] as UnitProgress[],
    courseProgress: [] as CourseProgress[],
    gameStats: {
      totalCoursesCompleted: 0,
      totalUnitsCompleted: 0,
      totalQuestionsAnswered: 0,
      totalCorrectAnswers: 0,
      averageScore: 0,
      totalTimeSpent: 0,
      streakDays: 0,
      lastPlayedDate: undefined,
    } as GameStats,
  }),

  getters: {
    // Get progress for a specific unit
    getUnitProgress: (state) => (unitId: number) => {
      return state.unitProgress.find(up => up.unitId === unitId)
    },

    // Get progress for a specific course
    getCourseProgress: (state) => (courseId: number) => {
      return state.courseProgress.find(cp => cp.courseId === courseId)
    },

    // Check if a unit is completed
    isUnitCompleted: (state) => (unitId: number) => {
      const progress = state.unitProgress.find(up => up.unitId === unitId)
      return progress?.completed || false
    },

    // Check if a course is completed
    isCourseCompleted: (state) => (courseId: number) => {
      const progress = state.courseProgress.find(cp => cp.courseId === courseId)
      return progress?.completed || false
    },

    // Get completed courses
    completedCourses: (state) => {
      return state.courseProgress.filter(cp => cp.completed)
    },

    // Get in-progress courses
    inProgressCourses: (state) => {
      return state.courseProgress.filter(cp => !cp.completed && cp.unitsCompleted > 0)
    },

    // Get overall completion percentage
    overallProgress: (state) => {
      if (state.courseProgress.length === 0) return 0
      const totalUnits = state.courseProgress.reduce((sum, cp) => sum + cp.totalUnits, 0)
      const completedUnits = state.courseProgress.reduce((sum, cp) => sum + cp.unitsCompleted, 0)
      return totalUnits > 0 ? Math.round((completedUnits / totalUnits) * 100) : 0
    },
  },

  actions: {
    // Complete a unit and update progress
    completeUnit(unitData: {
      unitId: number
      courseId: number
      courseTitle: string
      score: number
      totalQuestions: number
      correctAnswers: number
      timeSpent?: number
    }) {
      const now = new Date().toISOString()

      // Update or create unit progress
      const existingUnitIndex = this.unitProgress.findIndex(up => up.unitId === unitData.unitId)
      const unitProgress: UnitProgress = {
        unitId: unitData.unitId,
        courseId: unitData.courseId,
        completed: true,
        score: unitData.score,
        totalQuestions: unitData.totalQuestions,
        correctAnswers: unitData.correctAnswers,
        completedAt: now,
        timeSpent: unitData.timeSpent || 0,
      }

      if (existingUnitIndex >= 0) {
        this.unitProgress[existingUnitIndex] = unitProgress
      } else {
        this.unitProgress.push(unitProgress)
      }

      // Update course progress
      this.updateCourseProgress(unitData.courseId, unitData.courseTitle)

      // Update game stats
      this.updateGameStats()
    },

    // Update course progress based on completed units
    async updateCourseProgress(courseId: number, courseTitle: string) {
      try {
        // Get all units for this course to calculate total
        const { unitRepository } = await import('@/repositories/unit')
        const unitsResponse = await unitRepository.getUnitsByCourse(courseId)
        const allUnits = unitsResponse.data || []

        // Calculate course progress
        const courseUnits = this.unitProgress.filter(up => up.courseId === courseId)
        const completedUnits = courseUnits.filter(up => up.completed)
        const totalScore = completedUnits.reduce((sum, up) => sum + up.score, 0)
        const totalQuestions = completedUnits.reduce((sum, up) => sum + up.totalQuestions, 0)
        const totalCorrectAnswers = completedUnits.reduce((sum, up) => sum + up.correctAnswers, 0)

        const courseCompleted = completedUnits.length === allUnits.length && allUnits.length > 0

        // Update or create course progress
        const existingCourseIndex = this.courseProgress.findIndex(cp => cp.courseId === courseId)
        const courseProgress: CourseProgress = {
          courseId,
          courseTitle,
          unitsCompleted: completedUnits.length,
          totalUnits: allUnits.length,
          totalScore,
          totalQuestions,
          totalCorrectAnswers,
          completed: courseCompleted,
          completedAt: courseCompleted ? new Date().toISOString() : undefined,
          lastAccessedAt: new Date().toISOString(),
        }

        if (existingCourseIndex >= 0) {
          this.courseProgress[existingCourseIndex] = courseProgress
        } else {
          this.courseProgress.push(courseProgress)
        }
      } catch (error) {
        console.error('Error updating course progress:', error)
      }
    },

    // Update overall game statistics
    updateGameStats() {
      const completedCourses = this.courseProgress.filter(cp => cp.completed)
      const completedUnits = this.unitProgress.filter(up => up.completed)

      this.gameStats = {
        totalCoursesCompleted: completedCourses.length,
        totalUnitsCompleted: completedUnits.length,
        totalQuestionsAnswered: completedUnits.reduce((sum, up) => sum + up.totalQuestions, 0),
        totalCorrectAnswers: completedUnits.reduce((sum, up) => sum + up.correctAnswers, 0),
        averageScore: completedUnits.length > 0
          ? Math.round(completedUnits.reduce((sum, up) => sum + up.score, 0) / completedUnits.length)
          : 0,
        totalTimeSpent: completedUnits.reduce((sum, up) => sum + (up.timeSpent || 0), 0),
        streakDays: this.calculateStreakDays(),
        lastPlayedDate: new Date().toISOString(),
      }
    },

    // Calculate streak days (simplified - consecutive days of play)
    calculateStreakDays(): number {
      // This is a simplified implementation
      // In a real app, you'd track daily activity more precisely
      const today = new Date().toDateString()
      const lastPlayed = this.gameStats.lastPlayedDate ? new Date(this.gameStats.lastPlayedDate).toDateString() : null

      if (lastPlayed === today) {
        return this.gameStats.streakDays
      } else if (lastPlayed) {
        const yesterday = new Date()
        yesterday.setDate(yesterday.getDate() - 1)
        if (lastPlayed === yesterday.toDateString()) {
          return this.gameStats.streakDays + 1
        }
      }

      return 1 // Reset streak or start new streak
    },

    // Reset all progress (for testing or user request)
    resetProgress() {
      this.unitProgress = []
      this.courseProgress = []
      this.gameStats = {
        totalCoursesCompleted: 0,
        totalUnitsCompleted: 0,
        totalQuestionsAnswered: 0,
        totalCorrectAnswers: 0,
        averageScore: 0,
        totalTimeSpent: 0,
        streakDays: 0,
        lastPlayedDate: undefined,
      }
    },

    // Mark course as accessed (for tracking last accessed time)
    accessCourse(courseId: number, courseTitle: string) {
      const existingCourseIndex = this.courseProgress.findIndex(cp => cp.courseId === courseId)

      if (existingCourseIndex >= 0) {
        this.courseProgress[existingCourseIndex].lastAccessedAt = new Date().toISOString()
      } else {
        // Create new course progress entry
        this.courseProgress.push({
          courseId,
          courseTitle,
          unitsCompleted: 0,
          totalUnits: 0,
          totalScore: 0,
          totalQuestions: 0,
          totalCorrectAnswers: 0,
          completed: false,
          lastAccessedAt: new Date().toISOString(),
        })
      }
    },
  },

  persist: true,
})
