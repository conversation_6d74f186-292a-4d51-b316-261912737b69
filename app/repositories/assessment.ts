import type { AssessmentType } from '#shared/constants'
import type { Assessmentable, CreateAssessmentData, UpdateAssessmentData } from '@/types/course'

/**
 * Assessment Management Repository
 * Pure API calls for assessment management endpoints
 * Handles both admin and public assessment operations
 */
export const assessmentRepository = {
  /**
   * Create a new assessment (Admin)
   * @param assessmentData - Assessment creation data with type
   * @returns Promise resolving to created assessment
   */
  async createAssessment(assessmentData: CreateAssessmentData & { type: AssessmentType }): Promise<Assessmentable> {
    const { call } = useAPICall()
    return await call<Assessmentable>('/api/admin/assessments', {
      method: 'POST',
      body: assessmentData,
    })
  },

  /**
   * Update assessment (Admin)
   * @param type - Assessment type
   * @param id - Assessment ID
   * @param assessmentData - Assessment update data
   * @returns Promise resolving to updated assessment
   */
  async updateAssessment(type: AssessmentType, id: number, assessmentData: UpdateAssessmentData): Promise<Assessmentable> {
    const { call } = useAPICall()
    return await call<Assessmentable>(`/api/admin/assessments/${type}/${id}`, {
      method: 'PUT',
      body: assessmentData,
    })
  },

  /**
   * Delete assessment (Admin)
   * @param type - Assessment type
   * @param id - Assessment ID
   * @returns Promise resolving to deletion response
   */
  async deleteAssessment(type: AssessmentType, id: number): Promise<void> {
    const { call } = useAPICall()
    return await call<void>(`/api/admin/assessments/${type}/${id}`, {
      method: 'DELETE',
    })
  },

  /**
   * Clear all assessments from a unit (Admin)
   * @param unitId - Unit ID
   * @returns Promise resolving to deletion response with count
   */
  async clearUnitAssessments(unitId: number): Promise<{ deleted_count: number }> {
    const { call } = useAPICall()
    return await call<{ deleted_count: number }>(`/api/admin/units/${unitId}/assessments`, {
      method: 'DELETE',
    })
  },
}
