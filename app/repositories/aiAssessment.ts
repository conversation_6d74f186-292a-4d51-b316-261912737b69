import type { AssessmentType } from '#shared/constants'

/**
 * AI Assessment Testing Repository
 * Pure API calls for AI assessment testing endpoints
 * Handles fetching assessments by type for testing purposes
 */

export interface AiAssessmentItem {
  unit_id: number
  question: string
  assessment_id: number
  fill_position: number[]
}

export interface TestPromptRequest {
  unit_id: number
  answers: Array<{
    assessment_id: number
    answer: string
  }>
}

export interface TestPromptResponse {
  success: boolean
  message: string
  data: {
    final_prompt: string
    model_info: {
      model_name: string
      tokens_consumed: number
      processing_time_ms: number
      input_tokens: number
      output_tokens: number
    }
    ai_output: Array<{
      id: number
      point: string
      comment: string
    }>
  }
}

export const aiAssessmentRepository = {
  /**
   * Fetch AI assessments by type for testing
   * @param type - Assessment type (e.g., 'ai_gap_fill_sentence')
   * @returns Promise resolving to array of assessment items
   */
  async fetchAssessmentsByType(type: AssessmentType): Promise<AiAssessmentItem[]> {
    const { call } = useAPICall()

    try {
      return await call<AiAssessmentItem[]>(`/api/admin/ai-prompts/assessments/${type}`, {
        method: 'GET',
      })
    } catch (error: any) {
      // Handle specific error cases
      if (error?.statusCode === 400) {
        throw new Error(`Invalid assessment type: ${type}`)
      }
      if (error?.statusCode === 500) {
        throw new Error('Server error occurred while fetching assessments')
      }
      throw error
    }
  },

  /**
   * Test AI prompt generation without affecting actual scoring
   * @param request - Test prompt request data
   * @returns Promise resolving to test prompt response
   */
  async testPrompt(request: TestPromptRequest): Promise<TestPromptResponse> {
    const { call } = useAPICall()

    try {
      return await call<TestPromptResponse>('/api/admin/ai-prompts/test-prompt', {
        method: 'POST',
        body: request,
      })
    } catch (error: any) {
      // Handle specific error cases
      if (error?.statusCode === 400) {
        throw new Error('Invalid request data')
      }
      if (error?.statusCode === 404) {
        throw new Error('Assessment not found')
      }
      if (error?.statusCode === 500) {
        throw new Error('Failed to generate test prompt')
      }
      throw error
    }
  },
}
