import type { ImportResponse } from '@/types/course'
import { TOKEN_AUTH_KEY, AssessmentType } from '#shared/constants'

/**
 * CSV Import Repository
 * Pure API calls for CSV import functionality
 * Handles file uploads and validation for course content import
 */
export const csvImportRepository = {
  /**
   * Import CSV file (Admin)
   * @param csvFile - CSV file to import
   * @param unitId - Target unit ID
   * @returns Promise resolving to import results
   */
  async importCSV(csvFile: File, unitId: number): Promise<ImportResponse> {
    const config = useRuntimeConfig()
    const tokenCookie = useCookie(TOKEN_AUTH_KEY, { default: () => '' })

    const formData = new FormData()
    formData.append('file', csvFile)

    // Ensure we have a valid token
    if (!tokenCookie.value) {
      throw new Error('Authentication token is required')
    }

    return await $fetch<ImportResponse>(`/api/admin/units/${unitId}/import/csv`, {
      baseURL: config.public.apiBaseUrl as string,
      method: 'POST',
      headers: {
        Authorization: `Bearer ${tokenCookie.value}`,
        Accept: 'application/json',
      },
      body: formData,
    })
  },

  /**
   * Download CSV template (Admin)
   * @returns Promise resolving to CSV template file
   */
  async downloadTemplate(assessment_type: AssessmentType): Promise<Blob> {
    const config = useRuntimeConfig()
    const tokenCookie = useCookie(TOKEN_AUTH_KEY, { default: () => '' })

    // Use $fetch directly to avoid automatic JSON headers
    const response = await $fetch(`/api/admin/import/template/${assessment_type}`, {
      baseURL: config.public.apiBaseUrl as string,
      method: 'GET',
      headers: {
        Authorization: tokenCookie.value ? `Bearer ${tokenCookie.value}` : '',
        Accept: 'text/csv,application/octet-stream,*/*',
      },
      responseType: 'blob',
    })

    return response as Blob
  },

  /**
   * Helper method to create a download link for the CSV template
   * @returns Promise that triggers the template download
   */
  async downloadTemplateFile(): Promise<void> {
    try {
      const blob = await this.downloadTemplate()

      // Create a temporary URL for the blob
      const url = window.URL.createObjectURL(blob)

      // Create a temporary anchor element and trigger download
      const link = document.createElement('a')
      link.href = url
      link.download = 'import_template.csv'
      document.body.appendChild(link)
      link.click()

      // Clean up
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
    }
    catch (error) {
      console.error('Failed to download template:', error)
      throw error
    }
  },

  /**
   * Helper method to validate file before upload
   * @param file - File to validate
   * @returns Boolean indicating if file is valid
   */
  validateFile(file: File): { isValid: boolean, error?: string } {
    // Check file type
    const allowedTypes = ['text/csv', 'text/plain', 'application/csv']
    const allowedExtensions = ['.csv', '.txt']

    const hasValidType = allowedTypes.includes(file.type)
    const hasValidExtension = allowedExtensions.some(ext =>
      file.name.toLowerCase().endsWith(ext),
    )

    if (!hasValidType && !hasValidExtension) {
      return {
        isValid: false,
        error: 'Invalid file type. Please upload a CSV or TXT file.',
      }
    }

    // Check file size (10MB limit)
    const maxSize = 10 * 1024 * 1024 // 10MB in bytes
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: 'File size exceeds 10MB limit.',
      }
    }

    return { isValid: true }
  },
}
