export const TOKEN_AUTH_KEY = 'auth'

export enum AssessmentType {
  MULTIPLE_SELECT = 'multiple_select',
  AI_GAP_FILL_SENTENCE = 'ai_gap_fill_sentence',
}

export const skillTypeOptions = [
  { value: 'vocabulary', label: 'Vocabulary' },
  { value: 'grammar', label: 'Grammar' },
  { value: 'listening', label: 'Listening' },
  { value: 'reading', label: 'Reading' },
  { value: 'speaking', label: 'Speaking' },
  { value: 'writing', label: 'Writing' },
]

export const difficultyOptions = [
  { value: 'beginner', label: 'Beginner' },
  { value: 'elementary', label: 'Elementary' },
  { value: 'intermediate', label: 'Intermediate' },
  { value: 'upper_intermediate', label: 'Upper Intermediate' },
  { value: 'advanced', label: 'Advanced' },
  { value: 'proficient', label: 'Proficient' },
]
